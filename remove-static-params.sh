#!/bin/bash

# Danh sách các file cần xóa hàm generateStaticParams()
files=(
  "src/app/dashboard/tour/[id]/edit/page.jsx"
  "src/app/dashboard/tour/[id]/page.jsx"
  "src/app/dashboard/invoice/[id]/edit/page.jsx"
  "src/app/dashboard/job/[id]/edit/page.jsx"
  "src/app/dashboard/invoice/[id]/page.jsx"
  "src/app/dashboard/job/[id]/page.jsx"
  "src/app/dashboard/order/[id]/page.jsx"
  "src/app/dashboard/user/[id]/edit/page.jsx"
)

# Xóa hàm generateStaticParams() trong mỗi file
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    # Sử dụng sed để xóa hàm generateStaticParams() và comment liên quan
    sed -i '/\/\/ ----------------------------------------------------------------------/,/^export async function generateStaticParams()/d' "$file"
    sed -i '/^export async function generateStaticParams()/,/^}/d' "$file"
    echo "Đã xóa hàm generateStaticParams() trong file $file"
  else
    echo "File $file không tồn tại"
  fi
done

echo "Hoàn thành!"
