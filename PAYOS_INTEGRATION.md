# Hướng dẫn tích hợp PayOS

## Giới thiệu

Tài liệu này hướng dẫn cách tích hợp PayOS vào ứng dụng một cách an toàn, đảm bảo các thông tin xác thực được bảo mật.

## Cấu hình môi trường

1. Tạo file `.env.local` từ file `.env.example`
2. Cập nhật các biến môi trường PayOS:
   ```
   PAYOS_CLIENT_ID=your-payos-client-id
   PAYOS_API_KEY=your-payos-api-key
   PAYOS_CHECKSUM_KEY=your-payos-checksum-key
   ```

> **Lưu ý quan trọng**: Không sử dụng tiền tố `NEXT_PUBLIC_` cho các biến môi trường PayOS để đảm bảo thông tin xác thực không bị lộ ra phía client.

## C<PERSON>u trúc API

Ứng dụng sử dụng các API route sau để tương tác với PayOS:

1. **Tạo link thanh toán**:
   - Route: `/api/payos/create-payment`
   - Method: POST
   - Body: 
     ```json
     {
       "orderCode": "ORDER123",
       "amount": 50000,
       "description": "Thanh toán đơn hàng #123",
       "returnUrl": "https://example.com/success",
       "cancelUrl": "https://example.com/cancel",
       "buyerName": "Nguyễn Văn A",
       "buyerEmail": "<EMAIL>",
       "buyerPhone": "0123456789",
       "items": []
     }
     ```

2. **Lấy thông tin thanh toán**:
   - Route: `/api/payos/get-payment-info?orderId=ORDER123`
   - Method: GET

3. **Hủy thanh toán**:
   - Route: `/api/payos/cancel-payment`
   - Method: POST
   - Body:
     ```json
     {
       "orderId": "ORDER123",
       "cancellationReason": "Lý do hủy"
     }
     ```

4. **Webhook**:
   - Route: `/api/payos-webhook`
   - Method: POST
   - Cấu hình webhook này trong trang quản trị PayOS

## Xác thực Webhook

Webhook từ PayOS được xác thực bằng chữ ký (signature) để đảm bảo tính toàn vẹn của dữ liệu. Quá trình xác thực được thực hiện trong file `src/utils/payos-utils.js`.

## Sử dụng trong ứng dụng

```javascript
import { createPaymentLink, getPaymentInfo, cancelPaymentLink } from 'src/actions/mooly-chatbot/payos-service';

// Tạo link thanh toán
const createPayment = async () => {
  const paymentData = {
    orderCode: 'ORDER123',
    amount: 50000,
    description: 'Thanh toán đơn hàng #123',
    returnUrl: 'https://example.com/success',
    cancelUrl: 'https://example.com/cancel',
  };
  
  const result = await createPaymentLink(paymentData);
  
  if (result.success) {
    // Chuyển hướng đến trang thanh toán
    window.location.href = result.data.checkoutUrl;
  } else {
    console.error('Lỗi tạo link thanh toán:', result.error);
  }
};

// Lấy thông tin thanh toán
const getPayment = async (orderId) => {
  const result = await getPaymentInfo(orderId);
  
  if (result.success) {
    console.log('Thông tin thanh toán:', result.data);
  } else {
    console.error('Lỗi lấy thông tin thanh toán:', result.error);
  }
};

// Hủy thanh toán
const cancelPayment = async (orderId) => {
  const result = await cancelPaymentLink(orderId, 'Khách hàng hủy đơn hàng');
  
  if (result.success) {
    console.log('Đã hủy thanh toán thành công');
  } else {
    console.error('Lỗi hủy thanh toán:', result.error);
  }
};
```

## Xử lý kết quả thanh toán

1. **Qua returnUrl**: Khi thanh toán thành công, PayOS sẽ chuyển hướng người dùng đến `returnUrl` với các tham số trên URL.

2. **Qua webhook**: PayOS sẽ gửi thông báo đến webhook đã cấu hình. Webhook này sẽ xử lý cập nhật trạng thái thanh toán trong cơ sở dữ liệu.

## Bảo mật

1. **Không lưu thông tin xác thực ở client**: Tất cả các thông tin xác thực (clientId, apiKey, checksumKey) đều được lưu trữ ở server-side.

2. **Xác thực chữ ký**: Tất cả các webhook từ PayOS đều được xác thực chữ ký để đảm bảo tính toàn vẹn của dữ liệu.

3. **Kiểm tra dữ liệu đầu vào**: Tất cả các dữ liệu đầu vào đều được kiểm tra trước khi xử lý.

## Tài liệu tham khảo

- [Tài liệu API PayOS](https://payos.vn/docs/api/)
- [Kiểm tra dữ liệu với signature](https://payos.vn/docs/tich-hop-webhook/kiem-tra-du-lieu-voi-signature/)
