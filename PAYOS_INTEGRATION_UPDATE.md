# Cập nhật tích hợp PayOS

## Tổng quan

Tài liệu này mô tả các cập nhật đã thực hiện để tích hợp PayOS theo tài liệu API mới nhất. <PERSON><PERSON><PERSON> thay đổi chính bao gồm:

1. <PERSON><PERSON><PERSON><PERSON> chữ ký (signature) cho các yêu cầu API
2. C<PERSON><PERSON> nhật cấu trúc dữ liệu theo yêu cầu mới
3. <PERSON><PERSON>i thiện xử lý lỗi và bảo mật

## Các file đã cập nhật

1. `src/utils/payos-utils.js` - Thêm hàm tạo chữ ký
2. `src/app/api/payos/create-payment/route.js` - Cập nhật API tạo thanh toán
3. `src/actions/mooly-chatbot/payos-service.js` - <PERSON><PERSON>i thiện xử lý dữ liệu đầu vào
4. `src/app/api/payos-webhook/route.js` - <PERSON><PERSON><PERSON> nhật xử lý webhook

## Chi tiết cập nhật

### 1. <PERSON><PERSON><PERSON> chữ ký (signature)

<PERSON> tài liệu mới của PayOS, mỗi yêu cầu tạo thanh toán cần có chữ ký để xác thực. Chữ ký được tạo bằng cách:

1. Sắp xếp các trường theo thứ tự alphabet: `amount`, `cancelUrl`, `description`, `orderCode`, `returnUrl`
2. Chuyển đổi thành chuỗi truy vấn: `amount=56000000&cancelUrl=https://your-cancel-url.com&description=VQRIO123&orderCode=123&returnUrl=https://your-success-url.com`
3. Tạo chữ ký bằng HMAC-SHA256 với checksumKey

```javascript
// Hàm tạo chữ ký
export function createSignature(data, checksumKey) {
  try {
    // Lọc các trường cần thiết cho signature theo tài liệu PayOS
    const signatureData = {
      amount: data.amount,
      cancelUrl: data.cancelUrl,
      description: data.description,
      orderCode: data.orderCode,
      returnUrl: data.returnUrl,
    };
    
    // Sắp xếp dữ liệu theo alphabet
    const sortedData = sortObjectByKey(signatureData);
    
    // Chuyển đổi dữ liệu thành chuỗi truy vấn
    const queryString = convertObjectToQueryString(sortedData);
    
    // Tạo chữ ký từ dữ liệu
    const hmac = crypto.createHmac('sha256', checksumKey);
    return hmac.update(queryString).digest('hex');
  } catch (error) {
    console.error('Error creating signature:', error);
    throw new Error('Failed to create signature');
  }
}
```

### 2. Cập nhật API tạo thanh toán

API tạo thanh toán đã được cập nhật để thêm chữ ký và hỗ trợ các trường mới:

```javascript
// Chuẩn bị dữ liệu gửi đến PayOS theo cấu trúc mới nhất
const requestData = {
  orderCode,
  amount,
  description,
  cancelUrl,
  returnUrl,
};

// Thêm các trường tùy chọn
if (buyerName) requestData.buyerName = buyerName;
if (buyerEmail) requestData.buyerEmail = buyerEmail;
if (buyerPhone) requestData.buyerPhone = buyerPhone;
if (buyerAddress) requestData.buyerAddress = buyerAddress;
if (expiredAt) requestData.expiredAt = expiredAt;

// Tạo chữ ký cho request
requestData.signature = createSignature(requestData, PAYOS_CONFIG.checksumKey);
```

### 3. Cải thiện xử lý dữ liệu đầu vào

Client-side service đã được cập nhật để xử lý dữ liệu đầu vào tốt hơn:

- Chuyển đổi `amount` thành số nguyên
- Hỗ trợ thời gian hết hạn (expiredAt) dưới dạng Unix timestamp
- Hỗ trợ tùy chọn expiryTime (số giờ) để tính toán expiredAt

```javascript
// Thêm thời gian hết hạn nếu có (Unix timestamp)
if (paymentData.expiredAt) {
  requestData.expiredAt = paymentData.expiredAt;
} else if (paymentData.expiryTime) {
  // Nếu có expiryTime (số giờ), tính toán Unix timestamp
  const expiryHours = parseInt(paymentData.expiryTime, 10) || 24; // Mặc định 24 giờ
  const expiredAt = Math.floor(Date.now() / 1000) + expiryHours * 3600;
  requestData.expiredAt = expiredAt;
}
```

### 4. Cập nhật xử lý webhook

Webhook đã được cập nhật để xử lý dữ liệu từ PayOS theo định dạng mới:

- Hỗ trợ cả `orderCode` và `order_code` để tương thích với các phiên bản API
- Cải thiện xử lý lỗi và kiểm tra dữ liệu đầu vào

```javascript
// Trong webhook mới của PayOS, orderCode nằm trong data.orderCode
const orderCode = data.orderCode || data.order_code || '';

if (!orderCode) {
  return NextResponse.json(
    { success: false, message: 'Invalid order code in webhook data' },
    { status: 400 }
  );
}
```

## Cấu hình môi trường

Đảm bảo các biến môi trường sau được cấu hình trong file `.env.local`:

```
PAYOS_CLIENT_ID=your-payos-client-id
PAYOS_API_KEY=your-payos-api-key
PAYOS_CHECKSUM_KEY=your-payos-checksum-key
```

> **Lưu ý quan trọng**: Không sử dụng tiền tố `NEXT_PUBLIC_` cho các biến môi trường PayOS để đảm bảo thông tin xác thực không bị lộ ra phía client.

## Cấu hình webhook

Đảm bảo webhook được cấu hình trong trang quản trị PayOS để trỏ đến:

```
https://your-domain.com/api/payos-webhook
```

## Kiểm tra

Để kiểm tra tích hợp, bạn có thể sử dụng các API sau:

1. Tạo thanh toán: `POST /api/payos/create-payment`
2. Lấy thông tin thanh toán: `GET /api/payos/get-payment-info?orderId=ORDER123`
3. Hủy thanh toán: `POST /api/payos/cancel-payment`

## Tài liệu tham khảo

- [Tài liệu API PayOS](https://payos.vn/docs/api/)
- [Kiểm tra dữ liệu với signature](https://payos.vn/docs/tich-hop-webhook/kiem-tra-du-lieu-voi-signature/)
