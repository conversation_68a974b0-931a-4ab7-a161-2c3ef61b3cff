# Sửa lỗi tích hợp PayOS

## Vấn đề

Khi tạo thanh toán với PayOS, hệ thống gặp lỗi:

```json
{
    "success": false,
    "error": "order_code must not be greater than 9007199254740991,order_code must be a positive number,orderCode must be a number conforming to the specified constraints, Mô tả tối đa 25 kí tự",
    "data": null
}
```

## Nguyên nhân

1. PayOS yêu cầu `orderCode` phải là số nguyên dương, không phải UUID
2. <PERSON><PERSON> tả thanh toán không được vượt quá 25 ký tự
3. <PERSON><PERSON>u trúc dữ liệu gửi đến PayOS không đúng định dạng

## Các thay đổi đã thực hiện

### 1. Cập nhật `src/actions/mooly-chatbot/payos-service.js`

- <PERSON>hay thế UUID bằng số nguyên dương dựa trên timestamp
- Giới hạn mô tả tối đa 25 ký tự
- Cải thiện xử lý dữ liệu đầu vào

```javascript
// Tạo orderCode là số nguyên dương theo yêu cầu của PayOS
const timestamp = Date.now();
const randomNum = Math.floor(Math.random() * 10000);
const numericOrderCode = parseInt(`${timestamp}${randomNum}`.slice(0, 15), 10);

// Giới hạn mô tả tối đa 25 ký tự theo yêu cầu của PayOS
const shortDescription = (paymentData.description || '').slice(0, 25);

const requestData = {
  orderCode: numericOrderCode, // Sử dụng mã số thay vì UUID
  amount: parseInt(paymentData.amount, 10), // Đảm bảo amount là số nguyên
  description: shortDescription,
  returnUrl: paymentData.returnUrl,
  cancelUrl: paymentData.cancelUrl,
};
```

### 2. Cập nhật `src/app/api/payos/create-payment/route.js`

- Thêm kiểm tra orderCode phải là số nguyên dương
- Thêm kiểm tra độ dài mô tả

```javascript
// Kiểm tra orderCode phải là số dương
if (typeof orderCode !== 'number' || orderCode <= 0 || !Number.isInteger(orderCode)) {
  return NextResponse.json(
    {
      success: false,
      error: 'orderCode must be a positive integer',
      data: null,
    },
    { status: 400 }
  );
}

// Kiểm tra độ dài mô tả
if (description.length > 25) {
  return NextResponse.json(
    {
      success: false,
      error: 'Description must not exceed 25 characters',
      data: null,
    },
    { status: 400 }
  );
}
```

### 3. Cập nhật `src/actions/mooly-chatbot/credit-service.js`

- Thêm tạo ID thanh toán duy nhất (UUID) cho bảng credit_payments
- Lưu ID thanh toán để sử dụng trong returnUrl và cancelUrl

```javascript
// Tạo ID thanh toán duy nhất (UUID v4)
const paymentId = paymentData.paymentId || crypto.randomUUID();

// Tạo payment với trạng thái pending
return createData(CREDIT_PAYMENTS_TABLE, {
  packageId,
  amount,
  creditAmount,
  paymentMethod,
  paymentStatus: PAYMENT_STATUSES.PENDING,
  paymentId, // Lưu ID thanh toán để sử dụng trong returnUrl và cancelUrl
  createdBy,
});
```

### 4. Cập nhật `src/app/api/payos-webhook/route.js`

- Cải thiện xử lý webhook để tìm thanh toán theo nhiều cách
- Hỗ trợ cả orderCode và order_code để tương thích với các phiên bản API

```javascript
// Tìm thanh toán theo payment_id
let { data: paymentData, error: paymentError } = await supabase
  .from('credit_payments')
  .select('*')
  .eq('payment_id', orderCode)
  .single();
  
// Nếu không tìm thấy, có thể orderCode là ID nội bộ của PayOS
// Thử tìm trong payment_data
if (paymentError || !paymentData) {
  const { data: payments, error: paymentsError } = await supabase
    .from('credit_payments')
    .select('*')
    .eq('payment_status', PAYMENT_STATUSES.PENDING);
    
  if (!paymentsError && payments && payments.length > 0) {
    // Tìm payment có chứa orderCode trong payment_data
    for (const payment of payments) {
      if (payment.payment_data && 
          (payment.payment_data.orderCode === orderCode || 
           payment.payment_data.order_code === orderCode)) {
        paymentData = payment;
        paymentError = null;
        break;
      }
    }
  }
}
```

## Quy trình thanh toán mới

1. Tạo thanh toán trong bảng `credit_payments` với UUID làm `paymentId`
2. Khi gọi API PayOS, sử dụng số nguyên dương làm `orderCode` thay vì UUID
3. Lưu mối quan hệ giữa UUID gốc và orderCode của PayOS
4. Khi nhận webhook từ PayOS, tìm thanh toán dựa trên orderCode hoặc trong payment_data

## Kiểm tra

Để kiểm tra tích hợp, bạn có thể sử dụng các API sau:

1. Tạo thanh toán: `POST /api/payos/create-payment`
2. Lấy thông tin thanh toán: `GET /api/payos/get-payment-info?orderId=ORDER123`
3. Hủy thanh toán: `POST /api/payos/cancel-payment`

## Lưu ý

- Đảm bảo mô tả thanh toán không vượt quá 25 ký tự
- Đảm bảo orderCode là số nguyên dương
- Đảm bảo các URL callback (returnUrl, cancelUrl) được cấu hình đúng
