# Hướng dẫn xử lý ngày tháng trong dự án

Tài liệu này cung cấp hướng dẫn về cách xử lý ngày tháng trong dự án một cách chuẩn hóa và đồng bộ.

## Thư viện sử dụng

Dự án sử dụng **dayjs** làm thư viện chính để xử lý ngày tháng. Không nên sử dụng các thư viện khác như date-fns hoặc moment.js để tránh sự không đồng nhất.

```javascript
// ✅ Đúng
import dayjs from 'dayjs';
import { fDate, fDateTime } from 'src/utils/format-time';

// ❌ Sai
import { format } from 'date-fns';
import moment from 'moment';
```

## Các hàm tiện ích

### 1. Định dạng ngày tháng

Sử dụng các hàm tiện ích từ `src/utils/format-time.js`:

```javascript
import { fDate, fDateTime, fTime } from 'src/utils/format-time';

// Định dạng ngày: 17 Apr 2022
const formattedDate = fDate(date);

// Định dạng ngày giờ: 17 Apr 2022 12:00 am
const formattedDateTime = fDateTime(date);

// Định dạng giờ: 12:00 am
const formattedTime = fTime(date);
```

### 2. Các hàm tiện ích khác

Sử dụng các hàm tiện ích từ `src/utils/date-utils.js`:

```javascript
import {
  isValidDate,
  toDate,
  toISOString,
  getCurrentDate,
  getFirstDayOfMonth,
  getLastDayOfMonth,
  addTime,
  subtractTime,
  getDiff,
} from 'src/utils/date-utils';

// Kiểm tra ngày hợp lệ
if (isValidDate(date)) {
  // ...
}

// Chuyển đổi thành đối tượng Date
const dateObj = toDate(dateString);

// Chuyển đổi thành chuỗi ISO
const isoString = toISOString(date);

// Lấy ngày hiện tại
const today = getCurrentDate();

// Lấy ngày đầu tiên của tháng
const firstDay = getFirstDayOfMonth();

// Lấy ngày cuối cùng của tháng
const lastDay = getLastDayOfMonth();

// Thêm thời gian
const nextWeek = addTime(today, 7, 'day');

// Trừ thời gian
const lastWeek = subtractTime(today, 7, 'day');

// Tính khoảng cách
const daysDiff = getDiff(date1, date2, 'day');
```

## Định dạng chuẩn

Sử dụng các mẫu định dạng từ `DATE_FORMATS` trong `src/utils/date-utils.js`:

```javascript
import { DATE_FORMATS } from 'src/utils/date-utils';
import { fDate, fDateTime } from 'src/utils/format-time';

// Định dạng ngày theo kiểu Việt Nam: DD/MM/YYYY
const vietnameseDate = fDate(date, DATE_FORMATS.viDate);

// Định dạng ngày giờ theo kiểu Việt Nam: DD/MM/YYYY HH:mm
const vietnameseDateTime = fDateTime(date, DATE_FORMATS.viDateTime);
```

## Xử lý locale

Dự án đã cấu hình locale cho dayjs thông qua `LocalizationProvider` trong `src/locales/localization-provider.jsx`. Không cần cấu hình locale thủ công trong các component.

```javascript
// ✅ Đúng - Sử dụng hàm tiện ích đã được cấu hình locale
import { fDate } from 'src/utils/format-time';
const formattedDate = fDate(date);

// ❌ Sai - Cấu hình locale thủ công
import dayjs from 'dayjs';
import 'dayjs/locale/vi';
dayjs.locale('vi');
const formattedDate = dayjs(date).format('DD/MM/YYYY');
```

## Sử dụng với MUI Date Pickers

Dự án đã cấu hình `AdapterDayjs` cho MUI Date Pickers thông qua `LocalizationProvider`. Khi sử dụng các component Date Picker, không cần cấu hình adapter thủ công.

```jsx
// ✅ Đúng
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

function MyComponent() {
  return (
    <DatePicker
      label="Ngày"
      value={date}
      onChange={(newValue) => setDate(newValue)}
    />
  );
}
```

## Sử dụng với React Hook Form

Khi sử dụng với React Hook Form, nên sử dụng các component từ `src/components/hook-form`:

```jsx
import { Field } from 'src/components/hook-form';

function MyForm() {
  return (
    <Form methods={methods} onSubmit={onSubmit}>
      <Field.DatePicker name="date" label="Ngày" />
      <Field.DateTimePicker name="dateTime" label="Ngày giờ" />
    </Form>
  );
}
```

## Xử lý múi giờ

Dự án hiện tại không có xử lý múi giờ cụ thể. Nếu cần xử lý múi giờ, nên sử dụng plugin `utc` của dayjs:

```javascript
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

// Chuyển đổi sang UTC
const utcDate = dayjs(date).utc();

// Chuyển đổi sang múi giờ cụ thể
const localDate = dayjs(date).tz('Asia/Ho_Chi_Minh');
```

## Quy tắc chung

1. **Nhất quán**: Luôn sử dụng các hàm tiện ích từ `src/utils/format-time.js` và `src/utils/date-utils.js`
2. **Đơn giản**: Không tự cài đặt các hàm xử lý ngày tháng khi đã có sẵn
3. **Rõ ràng**: Sử dụng các hằng số định dạng từ `DATE_FORMATS` thay vì hardcode
4. **Tương thích**: Đảm bảo xử lý ngày tháng phù hợp với locale hiện tại của ứng dụng
