# Sử dụng Node.js 20 Alpine làm base image cho giai đoạn build
FROM node:20-alpine AS builder

# Thiết lập thư mục làm việc
WORKDIR /app

# Cài đặt các dependencies cần thiết
RUN apk add --no-cache libc6-compat

# Sao chép package.json và yarn.lock
COPY package.json yarn.lock ./

# Cài đặt dependencies
RUN yarn install --frozen-lockfile

# Sao chép toàn bộ source code
COPY . .

# Tạo file .env từ .env.example nếu cần
# RUN if [ -f .env.example ] && [ ! -f .env ]; then cp .env.example .env; fi

# Build ứng dụng
RUN yarn build

# Giai đoạn production - sử dụng image nhẹ hơn
FROM node:20-alpine AS runner

# Thiết lập thư mục làm việc
WORKDIR /app

# Cài đặt các dependencies cần thiết cho production
RUN apk add --no-cache libc6-compat

# Thiết lập biến môi trường NODE_ENV
ENV NODE_ENV production

# Tạo người dùng không có quyền root để chạy ứng dụng
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# Sao chép các file cần thiết từ giai đoạn build
COPY --from=builder /app/next.config.mjs ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json

# Sao chép thư mục .next đã được build
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next

# Cài đặt chỉ các dependencies production
COPY --from=builder /app/node_modules ./node_modules

# Chuyển quyền sở hữu cho người dùng nextjs
USER nextjs

# Expose port 3032 (theo cấu hình trong package.json)
EXPOSE 3032

# Thiết lập biến môi trường cho Next.js
ENV PORT 3032
ENV HOSTNAME "0.0.0.0"

# Chạy ứng dụng
CMD ["yarn", "start"]
