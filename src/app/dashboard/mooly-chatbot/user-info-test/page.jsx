'use client';

import { useState, useEffect } from 'react';

import { Box, Paper, Alert, Divider, Typography, CircularProgress } from '@mui/material';

import { createClient } from 'src/utils/supabase/client';

import { DashboardContent } from 'src/layouts/dashboard';
import { getCachedTenantId } from 'src/actions/mooly-chatbot/tenant-middleware';

export default function UserInfoTestPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userData, setUserData] = useState(null);
  const [tenantId, setTenantId] = useState(null);
  const [dbUserData, setDbUserData] = useState(null);

  useEffect(() => {
    async function fetchUserData() {
      try {
        setLoading(true);
        const supabase = createClient();

        // Lấy thông tin người dùng từ Auth
        const {
          data: { user },
          error: authError,
        } = await supabase.auth.getUser();

        if (authError) throw authError;
        if (!user) throw new Error('Không tìm thấy thông tin người dùng');

        setUserData(user);

        // Lấy tenant_id từ middleware
        const currentTenantId = await getCachedTenantId();
        setTenantId(currentTenantId);

        // Lấy thông tin người dùng từ bảng users
        const { data: dbUser, error: dbError } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single();

        if (dbError) throw dbError;
        setDbUserData(dbUser);
      } catch (err) {
        console.error('Lỗi khi lấy thông tin người dùng:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchUserData();
  }, []);

  const renderUserInfo = (title, data) => (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" sx={{ mb: 2 }}>
        {title}
      </Typography>
      <pre style={{ overflow: 'auto', maxHeight: '300px' }}>
        {JSON.stringify(data, null, 2)}
      </pre>
    </Paper>
  );

  return (
    <DashboardContent>
      <Typography variant="h4" sx={{ mb: 3 }}>
        Kiểm tra thông tin người dùng
      </Typography>

      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 5 }}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {!loading && !error && (
        <>
          <Alert severity="info" sx={{ mb: 3 }}>
            Tenant ID hiện tại: {tenantId || 'Không có'}
          </Alert>

          {userData && renderUserInfo('Thông tin từ Supabase Auth', userData)}
          
          <Divider sx={{ my: 3 }} />
          
          {dbUserData && renderUserInfo('Thông tin từ bảng users', dbUserData)}
        </>
      )}
    </DashboardContent>
  );
}
