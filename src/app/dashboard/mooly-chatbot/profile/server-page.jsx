import { redirect } from 'next/navigation';

import { Box, Grid, Paper, Avatar, Divider, Container, Typography } from '@mui/material';

import { createClient } from 'src/utils/supabase/server';

export default async function ServerProfilePage() {
  // Tạo Supabase client phía server
  const supabase = await createClient();

  // Lấy thông tin người dùng hiện tại
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  // Nếu không có người dùng hoặc có lỗi, chuyển hướng đến trang đăng nhập
  if (error || !user) {
    redirect('/auth/supabase/sign-in');
  }

  // L<PERSON>y thêm thông tin từ bảng users nếu cần
  const { data: userData } = await supabase
    .from('users')
    .select('tenant_id, role')
    .eq('id', user.id)
    .single();

  // Chuẩn bị dữ liệu người dùng
  const userProfile = {
    id: user.id,
    email: user.email,
    displayName: user.user_metadata?.display_name || 'Người dùng',
    photoURL: user.user_metadata?.avatar_url || '',
    role: userData?.role || 'user',
    tenantId: userData?.tenant_id || null,
  };

  return (
    <Container>
      <Typography variant="h4" sx={{ mb: 3 }}>
        Thông tin người dùng (Server Component)
      </Typography>
      <Paper sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Avatar
            src={userProfile.photoURL}
            alt={userProfile.displayName}
            sx={{ width: 80, height: 80, mr: 2 }}
          />
          <Box>
            <Typography variant="h5">{userProfile.displayName}</Typography>
            <Typography variant="body2" color="text.secondary">
              {userProfile.email}
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Grid container spacing={2}>
          <Grid item size={{ xs: 12, md: 6 }}>
            <Typography variant="subtitle1" fontWeight="bold">
              ID người dùng
            </Typography>
            <Typography variant="body2">{userProfile.id}</Typography>
          </Grid>
          <Grid item size={{ xs: 12, md: 6 }}>
            <Typography variant="subtitle1" fontWeight="bold">
              Vai trò
            </Typography>
            <Typography variant="body2">{userProfile.role}</Typography>
          </Grid>
          {userProfile.tenantId && (
            <Grid item size={{ xs: 12, md: 6 }}>
              <Typography variant="subtitle1" fontWeight="bold">
                Tenant ID
              </Typography>
              <Typography variant="body2">{userProfile.tenantId}</Typography>
            </Grid>
          )}
        </Grid>
      </Paper>
    </Container>
  );
}
