'use client';

import { useState, useEffect } from 'react';

import {
  Box,
  Grid,
  Paper,
  Alert,
  Button,
  Typography,
  CircularProgress,
} from '@mui/material';

import { DashboardContent } from 'src/layouts/dashboard';
import { fetchData } from 'src/actions/mooly-chatbot/supabase-utils';
import { getCachedTenantId, shouldAddTenantId } from 'src/actions/mooly-chatbot/tenant-middleware';

// Danh sách các bảng để kiểm tra
const TABLES_TO_CHECK = [
  'users',
  'products',
  'categories',
  'stores',
  'orders',
  'customers',
  'credit_packages',
  'credit_transactions',
];

export default function TenantTestPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tenantId, setTenantId] = useState(null);
  const [tableInfo, setTableInfo] = useState({});
  const [selectedTable, setSelectedTable] = useState(null);
  const [tableData, setTableData] = useState([]);
  const [loadingTableData, setLoadingTableData] = useState(false);

  useEffect(() => {
    async function fetchTenantInfo() {
      try {
        setLoading(true);
        
        // Lấy tenant_id hiện tại
        const currentTenantId = await getCachedTenantId();
        setTenantId(currentTenantId);
        
        // Kiểm tra các bảng có cần thêm tenant_id không
        const tablesInfo = {};
        TABLES_TO_CHECK.forEach((table) => {
          tablesInfo[table] = {
            name: table,
            needsTenantId: shouldAddTenantId(table),
          };
        });
        
        setTableInfo(tablesInfo);
      } catch (err) {
        console.error('Lỗi khi lấy thông tin tenant:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchTenantInfo();
  }, []);

  const handleFetchTableData = async (tableName) => {
    try {
      setSelectedTable(tableName);
      setLoadingTableData(true);
      
      // Lấy 5 bản ghi đầu tiên từ bảng
      const result = await fetchData(tableName, { limit: 5 });
      
      if (!result.success) {
        throw new Error(result.error || `Lỗi khi lấy dữ liệu từ bảng ${tableName}`);
      }
      
      setTableData(result.data || []);
    } catch (err) {
      console.error(`Lỗi khi lấy dữ liệu từ bảng ${tableName}:`, err);
      setError(err.message);
      setTableData([]);
    } finally {
      setLoadingTableData(false);
    }
  };

  return (
    <DashboardContent>
      <Typography variant="h4" sx={{ mb: 3 }}>
        Kiểm tra Tenant ID trong các truy vấn
      </Typography>

      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 5 }}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {!loading && !error && (
        <>
          <Alert severity="info" sx={{ mb: 3 }}>
            Tenant ID hiện tại: {tenantId || 'Không có'}
          </Alert>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Danh sách các bảng và cấu hình tenant_id
            </Typography>
            
            <Grid container spacing={2}>
              {Object.values(tableInfo).map((table) => (
                <Grid item xs={12} sm={6} md={4} key={table.name}>
                  <Paper 
                    elevation={3} 
                    sx={{ 
                      p: 2, 
                      display: 'flex', 
                      flexDirection: 'column',
                      height: '100%',
                      bgcolor: table.needsTenantId ? 'primary.lighter' : 'warning.lighter'
                    }}
                  >
                    <Typography variant="subtitle1" sx={{ mb: 1 }}>
                      {table.name}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 2, flex: 1 }}>
                      {table.needsTenantId 
                        ? 'Cần thêm tenant_id vào truy vấn' 
                        : 'Không cần thêm tenant_id'}
                    </Typography>
                    <Button 
                      variant="contained" 
                      size="small"
                      onClick={() => handleFetchTableData(table.name)}
                    >
                      Xem dữ liệu
                    </Button>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Paper>
          
          {selectedTable && (
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Dữ liệu từ bảng {selectedTable}
              </Typography>
              
              {loadingTableData ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                  <CircularProgress />
                </Box>
              ) : tableData.length > 0 ? (
                <pre style={{ overflow: 'auto', maxHeight: '400px' }}>
                  {JSON.stringify(tableData, null, 2)}
                </pre>
              ) : (
                <Alert severity="info">Không có dữ liệu trong bảng này</Alert>
              )}
            </Paper>
          )}
        </>
      )}
    </DashboardContent>
  );
}
