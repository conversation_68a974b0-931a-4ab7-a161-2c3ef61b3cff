# API Train Chatbot

API này sử dụng Vercel AI SDK để tạo ra dữ liệu tối ưu cho chatbot từ dữ liệu người dùng nhập vào.

## Cấu trúc dữ liệu

API nhận vào dữ liệu từ form chatbot training và trả về dữ liệu đã được tối ưu hóa với cấu trúc sau:

### Input

```json
{
  "name": "Tên doanh nghiệp/chatbot",
  "infoItems": [
    { "title": "Tiêu đề 1", "content": "Nội dung 1" },
    { "title": "Tiêu đề 2", "content": "Nội dung 2" }
  ],
  "instructionItems": [
    { "title": "Phong cách", "content": "Th<PERSON> thiện, chuyên nghiệp" },
    { "title": "<PERSON>ê<PERSON> cầu", "content": "Tr<PERSON> lời ngắn gọn, đ<PERSON>y đủ" }
  ],
  "instruction": "Hướng dẫn chung cho chatbot"
}
```

### Output

```json
{
  "success": true,
  "data": {
    "context": "Nội dung tổng hợp làm context mặc định cho chatbot",
    "instruction": "Hướng dẫn về tính cách và phong cách giao tiếp cho chatbot",
    "faqs": [
      {
        "topic": "Chủ đề 1",
        "content": "Nội dung chi tiết 1"
      },
      {
        "topic": "Chủ đề 2",
        "content": "Nội dung chi tiết 2"
      }
    ]
  },
  "message": "Đã tạo dữ liệu training thành công!"
}
```

## Cách sử dụng

1. Đảm bảo đã cấu hình OPENAI_API_KEY trong file .env.local
2. Gọi API từ client:

```javascript
const response = await fetch('/api/train-chatbot', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(trainingData),
});

const result = await response.json();
```

3. Hoặc sử dụng service đã được tạo sẵn:

```javascript
import { generateOptimizedTrainingData } from 'src/actions/mooly-chatbot/chatbot-ai-service';

const result = await generateOptimizedTrainingData(trainingData);
```

## Lưu ý

- API sử dụng mô hình GPT-4 Turbo của OpenAI thông qua Vercel AI SDK
- Dữ liệu trả về đã được tối ưu hóa để tiết kiệm token và tăng hiệu quả cho chatbot
- Cấu trúc dữ liệu trả về phù hợp để lưu trữ trong vector database và sử dụng làm context cho LLM
