import { z } from 'zod';
import { generateObject } from 'ai';
import { google } from '@ai-sdk/google';
import { NextResponse } from 'next/server';

import { withTenantAuth } from 'src/utils/server-auth';
import { createClient } from 'src/utils/supabase/server';

// Import trực tiếp các hằng số cần thiết

/**
 * API route để xử lý dữ liệu training chatbot và tạo ra cấu trúc dữ liệu tối ưu
 * @param {Request} request - Yêu cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const POST = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Lấy dữ liệu từ request
    const requestData = await request.json();

    // Kiểm tra dữ liệu đầu vào
    if (!requestData) {
      return NextResponse.json({
        success: false,
        error: 'Thiếu dữ liệu đầu vào'
      }, { status: 400 });
    }

    // Trích xuất dữ liệu từ request
    const { instruction = '' } = requestData;

    // Tính toán số credit cần sử dụng - mỗi lần training sẽ tiêu thụ 1 credit
    const creditAmount = 1;

    // Kết nối với Supabase
    const supabase = await createClient();

    // Kiểm tra credit của tenant bằng stored procedure check_tenant_credit
    const { data: creditCheckResult, error: creditCheckError } = await supabase
      .rpc('check_tenant_credit', { p_tenant_id: tenantId });

    if (creditCheckError) {
      return NextResponse.json(
        { success: false, error: creditCheckError.message },
        { status: 500 }
      );
    }

    // Kiểm tra kết quả từ stored procedure
    if (!creditCheckResult.success) {
      return NextResponse.json(
        { success: false, error: creditCheckResult.message },
        { status: 500 }
      );
    }

    // Kiểm tra xem tenant có đủ credit không
    if (!creditCheckResult.hasCredit) {
      return NextResponse.json(
        {
          success: false,
          error: 'Số dư credit không đủ để thực hiện training chatbot',
          data: { balance: creditCheckResult.balance || 0 }
        },
        { status: 400 }
      );
    }

    // Đã kiểm tra credit thành công, tiếp tục xử lý

    // Tạo prompt cho AI
    const prompt = `
    Bạn là chuyên gia tạo system prompt cho chatbot hỗ trợ khách hàng. Nhiệm vụ của bạn là tiếp nhận thông tin từ người dùng và tạo system prompt tối ưu, đúng chuẩn và tiết kiệm token.

Dưới đây là template system prompt cần hoàn thiện:

You are the automated virtual assistant of the store, named "{assistant_name}". Your mission is to support customers with the following tasks:
<tasks>
- Product consultation and recommendations
- Order creation and status checking
- Answering questions about products, policies, and services
- Basic complaint handling and order cancellation when orders are still in pending/processing status
- Transferring to staff when necessary (complex issues, serious complaints, spam)
</tasks>
<communication_rules>
- MUST use provided tools to get accurate information, NEVER fabricate information
- Always analyze conversation history to avoid asking for information already provided
- Respond naturally like a human, NEVER use phrases like "based on information" or "according to data"
- When customers refer to objects unclearly (it, that order...), proactively identify the object from context
- Always confirm the object naturally to ensure proper understanding
- Keep responses concise but complete with necessary information
- Respond in the same language the customer is using
- Only greet on first interaction to maintain natural conversation flow
</communication_rules>
<processing_workflow>
1. Receive question/request from customer
2. Carefully analyze conversation history to understand:
   a. Object customer is referring to
   b. Tools needed to retrieve information
   c. If you have enough information to respond
3. If enough information, respond directly without mentioning information sources
4. If insufficient information, use appropriate tools before answering
5. If question is irrelevant or cannot be answered, respond "I'm sorry, I can't help with this issue. Would you like me to transfer you to a consultant?"
</processing_workflow>
<question_response>
For each request, proceed as follows:
- First, analyze if you have enough information to answer accurately
- If yes, respond with facts directly, NEVER mentioning information sources
- Do not use phrases like "According to provided information" or "Based on data"
- If insufficient information, use provided tools to learn more
- For questions outside support scope, answer "I'm sorry, I can't help with this issue. Would you like me to transfer you to a consultant?"
</question_response>
<context>
Store information and policies are stored here for reference when answering customer queries.
{store_info}
{shipping_policy}
{return_policy}
{payment_methods}
{locations}
{working_hours}
{key_information}
</context>

Quy trình xử lý thông tin:
1. Xem xét toàn bộ thông tin được cung cấp dưới đây
2. Tóm tắt và cô đọng thông tin chỉ để giữ các điểm quan trọng nhất
3. Điền thông tin đã tối ưu vào các phần tương ứng trong template
4. Với phần {key_information}, hãy liệt kê ngắn gọn các chính sách và thông tin quan trọng nhất mà khách hàng thường hỏi (không cần định dạng Q&A, chỉ liệt kê thông tin chính)
5. Trả về system prompt hoàn chỉnh, đã được tối ưu token

Hãy hoàn thiện system prompt dựa trên thông tin về shop mà người dùng cung cấp, tự động gợi ý tên cho chatbot phù hợp nếu người dùng không cung cấp, đặt tên theo ngôn ngữ khách hàng.

Hãy hoàn thiện system prompt dựa trên thông tin sau:
${instruction}
    `;

    try {
      const schema = z.object({
        instruction: z.string().describe('Nội dung prompt đã được tối ưu, đúng chuẩn và tiết kiệm token theo template và thông tin người dùng cung cấp')
      });

      const { object } = await generateObject({
        model: google('gemini-2.5-flash-preview-04-17'),
        temperature: 0.2,
        schema,
        prompt,
      });

      // Training thành công, tiến hành trừ credit bằng stored procedure deduct_tenant_credit
      const { data: deductResult, error: deductError } = await supabase
        .rpc('deduct_tenant_credit', {
          p_tenant_id: tenantId,
          p_amount: creditAmount,
          p_description: 'Training chatbot',
          p_reference_type: 'chatbot_training',
          p_created_by: userId
        });

      if (deductError) {
        return NextResponse.json(
          { success: false, error: deductError.message },
          { status: 500 }
        );
      }

      // Kiểm tra kết quả từ stored procedure
      if (!deductResult.success) {
        return NextResponse.json(
          { success: false, error: deductResult.message },
          { status: 500 }
        );
      }

      // Lấy số dư mới từ kết quả của stored procedure
      const newBalance = deductResult.new_balance;

      // Kiểm tra và đảm bảo object có cấu trúc đúng
      if (!object || typeof object !== 'object') {
        return NextResponse.json(
          {
            success: false,
            error: 'Kết quả training không hợp lệ',
            message: 'Có lỗi xảy ra khi xử lý dữ liệu training'
          },
          { status: 500 }
        );
      }

      // Chuẩn bị dữ liệu trả về
      const responseData = {
        success: true,
        data: {
          // Đảm bảo instruction chứa prompt được tối ưu
          instruction: object.instruction || '',
          // Thêm thông tin về content gốc
          content: instruction,
          // Thêm các trường khác nếu cần
          context: object.context || '',
          faqs: Array.isArray(object.faqs) ? object.faqs : [],
        },
        message: 'Đã tạo dữ liệu training thành công!',
        credit: {
          used: creditAmount,
          balance: newBalance,
        },
      };

      // Trả về kết quả
      return NextResponse.json(responseData);
    } catch (trainingError) {

      const errorResponse = {
        success: false,
        error: trainingError.message || 'Lỗi khi tạo dữ liệu training',
        message: 'Có lỗi xảy ra khi xử lý dữ liệu training, không trừ credit',
      };

      return NextResponse.json(errorResponse, { status: 500 });
    }
  } catch (error) {

    const errorResponse = {
      success: false,
      error: error.message || 'Lỗi server',
      message: 'Có lỗi xảy ra khi xử lý dữ liệu training',
    };

    return NextResponse.json(errorResponse, { status: 500 });
  }
});
