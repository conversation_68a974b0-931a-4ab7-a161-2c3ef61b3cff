import { NextResponse } from 'next/server';

/**
 * API proxy để bật/tắt agent bot cho một inbox trong Mooly API
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - <PERSON><PERSON>n hồi HTTP
 */
export async function POST(request) {
  try {
    // Lấy thông tin từ body
    const body = await request.json();
    const { accountId, inboxId, token, agentBot } = body;

    // Nếu agentBot là true, sử dụng ID từ biến môi trường
    const finalAgentBot = agentBot === true ? process.env.MOOLY_AGENT_BOT_ID || '5' : null;

    // Kiểm tra dữ liệu đầu vào
    if (!accountId || !inboxId || !token) {
      return NextResponse.json(
        { success: false, error: 'Account ID, Inbox ID, and token are required' },
        { status: 400 }
      );
    }

    // L<PERSON>y thông tin cấu hình từ biến môi trường
    const host = process.env.MOOLY_API_HOST || 'https://app.mooly.vn';
    const apiVersion = process.env.MOOLY_API_VERSION || 'api/v1';

    // Tạo URL API
    const apiUrl = `${host}/${apiVersion}/accounts/${accountId}/inboxes/${inboxId}/set_agent_bot`;

    // Gọi API Mooly từ server
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        api_access_token: token,
      },
      body: JSON.stringify({
        agent_bot: finalAgentBot,
      }),
    });

    // Xử lý response từ API dựa trên status code
    const status = response.status;

    // Trường hợp thành công - 204 Success
    if (status === 204) {
      return NextResponse.json({ success: true, error: null, message: 'Success' }, { status: 200 });
    }

    // Xử lý các trường hợp lỗi cụ thể
    if (!response.ok) {
      // Trường hợp 403 - Access denied
      if (status === 403) {
        return NextResponse.json({ success: false, error: 'Access denied' }, { status: 403 });
      }

      // Trường hợp 404 - Inbox not found, Agent bot not found
      if (status === 404) {
        return NextResponse.json(
          { success: false, error: 'Inbox not found, Agent bot not found' },
          { status: 404 }
        );
      }

      // Các trường hợp lỗi khác
      let errorData;
      try {
        errorData = await response.json();
      // eslint-disable-next-line no-unused-vars
      } catch (_) {
        errorData = { message: response.statusText };
      }

      return NextResponse.json(
        {
          success: false,
          error: errorData.message || 'Failed to set agent bot',
        },
        { status: response.status }
      );
    }

    // Trường hợp thành công khác (nếu không phải 204)
    try {
      const data = await response.json();
      return NextResponse.json({ success: true, error: null, data });
    // eslint-disable-next-line no-unused-vars
    } catch (_) {
      // Nếu không có JSON body nhưng vẫn thành công
      return NextResponse.json({ success: true, error: null, message: 'Success' }, { status: 200 });
    }
  } catch (error) {
    console.error('Error in Mooly proxy set agent bot:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to set agent bot',
      },
      { status: 500 }
    );
  }
}
