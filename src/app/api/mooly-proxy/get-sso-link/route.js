import { NextResponse } from 'next/server';

/**
 * API proxy để lấy SSO link từ Mooly API
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - <PERSON><PERSON>n hồi HTTP
 */
export async function GET(request) {
  try {
    // L<PERSON>y thông tin từ URL params
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    // Sử dụng token từ biến môi trường thay vì từ URL params
    // Đây là token quản trị, không phải token của người dùng
    const token = process.env.MOOLY_PLATFORM_TOKEN;

    console.log('Debug - userId:', userId);
    console.log('Debug - token exists:', !!token);

    // Kiểm tra dữ liệu đầu vào
    if (!userId || !token) {
      return NextResponse.json(
        { success: false, error: 'User ID and token are required', data: null },
        { status: 400 }
      );
    }

    // L<PERSON>y thông tin cấu hình từ biến môi trường
    const host = process.env.MOOLY_API_HOST || 'https://app.mooly.vn';
    const apiVersion = process.env.MOOLY_API_VERSION || 'api/v1';

    console.log('Debug - API URL:', `${host}/platform/${apiVersion}/users/${userId}/login`);

    // Gọi API Mooly từ server
    console.log('Debug - Calling API:', `${host}/platform/${apiVersion}/users/${userId}/login`);

    const response = await fetch(`${host}/platform/${apiVersion}/users/${userId}/login`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'api_access_token': token,
      },
    });

    console.log('Debug - API response status:', response.status);

    // Xử lý lỗi từ API
    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      // eslint-disable-next-line no-unused-vars
      } catch (e) {
        errorData = { message: response.statusText };
      }

      return NextResponse.json(
        {
          success: false,
          error: errorData.message || 'Failed to get SSO link',
          data: null,
        },
        { status: response.status }
      );
    }

    // Trả về dữ liệu thành công
    const data = await response.json();
    return NextResponse.json({ success: true, error: null, data });
  } catch (error) {
    console.error('Error in Mooly proxy get SSO link:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to get SSO link',
        data: null,
      },
      { status: 500 }
    );
  }
}
