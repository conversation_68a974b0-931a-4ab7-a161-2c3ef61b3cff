import axios from 'axios';
import { NextResponse } from 'next/server';

import { withTenantAuth } from 'src/utils/server-auth';
import { createClient } from 'src/utils/supabase/server';

// Import các constant từ file create-chatwoot-account
import { LABEL_KEYS } from '../create-chatwoot-account/route';

// Mapping giữa key và title tiếng Việt
const LABEL_TITLE_MAP = {
  [LABEL_KEYS.SPAM]: 'Spam',
  [LABEL_KEYS.SUPPORT_NEEDED]: 'Cần hỗ trợ',
  [LABEL_KEYS.PURCHASE_INTENT]: 'Mua hàng',
  [LABEL_KEYS.COMPLAINT]: 'Khiếu nại',
  [LABEL_KEYS.CONSULTATION]: 'Tư vấn'
};

// Danh sách các label cần tạo
const PREDEFINED_LABELS = [
  {
    key: LABEL_KEYS.SPAM,
    title: LABEL_TITLE_MAP[LABEL_KEYS.SPAM],
    description: "Người dùng gửi tin nhắn spam (Label của hệ thống - KHÔNG ĐƯỢC XÓA)",
    color: "#FF5733",
    show_on_sidebar: true,
    system_label: true
  },
  {
    key: LABEL_KEYS.SUPPORT_NEEDED,
    title: LABEL_TITLE_MAP[LABEL_KEYS.SUPPORT_NEEDED],
    description: "Khách hàng cần hỗ trợ từ nhân viên (Label của hệ thống - KHÔNG ĐƯỢC XÓA)",
    color: "#33A1FF",
    show_on_sidebar: true,
    system_label: true
  },
  {
    key: LABEL_KEYS.PURCHASE_INTENT,
    title: LABEL_TITLE_MAP[LABEL_KEYS.PURCHASE_INTENT],
    description: "Khách hàng có ý định mua hàng (Label của hệ thống - KHÔNG ĐƯỢC XÓA)",
    color: "#33FF57",
    show_on_sidebar: true,
    system_label: true
  },
  {
    key: LABEL_KEYS.COMPLAINT,
    title: LABEL_TITLE_MAP[LABEL_KEYS.COMPLAINT],
    description: "Khách hàng có khiếu nại về sản phẩm/dịch vụ (Label của hệ thống - KHÔNG ĐƯỢC XÓA)",
    color: "#FF33A8",
    show_on_sidebar: true,
    system_label: true
  },
  {
    key: LABEL_KEYS.CONSULTATION,
    title: LABEL_TITLE_MAP[LABEL_KEYS.CONSULTATION],
    description: "Khách hàng cần tư vấn thêm thông tin (Label của hệ thống - KHÔNG ĐƯỢC XÓA)",
    color: "#FFD700",
    show_on_sidebar: true,
    system_label: true
  }
];

/**
 * Tạo các label cho tài khoản Chatwoot
 * @param {string} token - Token xác thực
 * @param {string} accountId - ID tài khoản
 * @returns {Promise<Object>} - Kết quả tạo label
 */
async function createLabels(token, accountId) {
  const host = process.env.MOOLY_API_HOST || 'https://app.mooly.vn';
  const apiVersion = process.env.MOOLY_API_VERSION || 'api/v1';
  const labelEndpoint = `${host}/${apiVersion}/accounts/${accountId}/labels`;

  const labelResults = {};

  // Tạo từng label và lưu kết quả
  for (const label of PREDEFINED_LABELS) {
    try {
      // Chuẩn bị dữ liệu label theo đúng định dạng API yêu cầu
      const labelData = {
        title: label.title,
        description: label.description,
        color: label.color,
        show_on_sidebar: label.show_on_sidebar
      };
      try {
        const response = await axios.post(labelEndpoint, { label: labelData }, {
          headers: {
            'Content-Type': 'application/json',
            'api_access_token': token
          }
        });

        // Axios trả về dữ liệu trong response.data
        const data = response.data;
        console.log(`Label created successfully:`, data);

        // Lưu ID của label với key là constant key thay vì title
        labelResults[label.key] = {
          id: data.id,
          title: data.title || label.title,
          description: data.description || label.description,
          color: data.color || label.color,
          show_on_sidebar: data.show_on_sidebar || label.show_on_sidebar,
          key: label.key,
          system_label: label.system_label || false
        };
      } catch (axiosError) {
        console.log(axiosError)
        console.error(`Failed to create label ${label.title}:`, axiosError.response?.data || axiosError.message);
        // Tiếp tục với label tiếp theo
      }
    } catch (error) {
      console.error(`Error creating label ${label.title}:`, error);
    }
  }

  return labelResults;
}

/**
 * Lưu cấu hình label vào database
 * @param {string} accountId - ID tài khoản Chatwoot
 * @param {Object} labelConfig - Cấu hình label
 * @param {string} tenantId - ID của tenant
 * @param {string} userId - ID của người dùng
 */
async function saveLabelsConfig(accountId, labelConfig, tenantId, userId) {
  try {
    // Chuẩn bị dữ liệu để lưu vào Supabase
    const formattedLabelConfig = {};

    // Chuyển đổi từ object thành cấu trúc dễ truy cập hơn
    Object.keys(labelConfig).forEach(key => {
      const label = labelConfig[key];
      formattedLabelConfig[key] = {
        id: label.id,
        title: label.title,
        description: label.description,
        color: label.color,
        show_on_sidebar: label.show_on_sidebar,
        system_label: label.system_label || false
      };
    });

    // Thêm metadata để dễ dàng quản lý
    const labelsMetadata = {
      labels: formattedLabelConfig,
      updated_at: new Date().toISOString(),
      version: '1.0'
    };

    const supabase = await createClient();
    const { error } = await supabase
      .from('mooly_accounts')
      .update({
        labels_config: labelsMetadata,
        updated_at: new Date().toISOString(),
        updated_by: userId
      })
      .eq('account_id', accountId)
      .eq('tenant_id', tenantId);

    if (error) {
      console.error('Error saving label config to database:', error);
      return { success: false, error: error.message };
    } else {
      console.log('Label config saved successfully for account:', accountId);
      return { success: true, data: labelsMetadata };
    }
  } catch (error) {
    console.error('Exception when saving label config:', error);
    return { success: false, error: error.message };
  }
}

/**
 * API endpoint để khởi tạo lại labels cho tài khoản Mooly
 * @param {Request} request - Yêu cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const POST = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Lấy thông tin từ body
    const body = await request.json();
    const { accountId, token } = body;

    // Kiểm tra dữ liệu đầu vào
    if (!accountId || !token) {
      return NextResponse.json(
        { success: false, error: 'Account ID and token are required' },
        { status: 400 }
      );
    }

    // Kiểm tra xem tài khoản có tồn tại trong database không
    const supabase = await createClient();
    const { data: accountData, error: accountError } = await supabase
      .from('mooly_accounts')
      .select('*')
      .eq('account_id', accountId)
      .eq('tenant_id', tenantId)
      .single();

    if (accountError || !accountData) {
      return NextResponse.json(
        { success: false, error: 'Account not found or not authorized' },
        { status: 404 }
      );
    }

    // Tạo các label cho tài khoản
    const labelConfig = await createLabels(token, accountId);

    // Kiểm tra xem có tạo được label nào không
    if (Object.keys(labelConfig).length === 0) {
      return NextResponse.json(
        { success: false, error: 'Failed to create any labels' },
        { status: 500 }
      );
    }

    // Lưu cấu hình label vào database
    const saveResult = await saveLabelsConfig(accountId, labelConfig, tenantId, userId);

    if (!saveResult.success) {
      return NextResponse.json(
        { success: false, error: saveResult.error },
        { status: 500 }
      );
    }

    // Trả về kết quả thành công
    return NextResponse.json({
      success: true,
      error: null,
      data: {
        labelsConfig: saveResult.data
      }
    });
  } catch (error) {
    console.error('Error in initialize labels:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to initialize labels',
        data: null,
      },
      { status: 500 }
    );
  }
});
