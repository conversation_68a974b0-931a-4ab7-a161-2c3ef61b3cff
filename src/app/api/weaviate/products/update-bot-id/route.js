import { NextResponse } from 'next/server';

import { withTenantAuth, validateTenantId } from 'src/utils/server-auth';

/**
 * API route để cập nhật bot_id cho sản phẩm
 * @param {Request} request - <PERSON><PERSON><PERSON> c<PERSON>u HTTP
 * @returns {Promise<NextResponse>} - <PERSON>ản hồi HTTP
 */
export const PUT = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Validate request body và kiểm tra tenant_id
    const validation = await validateTenantId(request, request.tenant_id);
    if (!validation.valid) {
      return NextResponse.json({ error: validation.error }, { status: validation.status });
    }

    const { product_id, bot_id, action } = validation.body;

    // Kiểm tra dữ liệu đầu vào
    if (!product_id || !bot_id) {
      return NextResponse.json(
        { error: 'Missing required fields: product_id, bot_id' },
        { status: 400 }
      );
    }

    if (!['add', 'remove'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be "add" or "remove"' },
        { status: 400 }
      );
    }

    // Lấy thông tin sản phẩm hiện tại
    const { data: product, error: fetchError } = await request.supabase
      .from('products')
      .select('bot_id')
      .eq('id', product_id)
      .eq('tenant_id', request.tenant_id)
      .single();
    if (fetchError) {
      return NextResponse.json(
        { error: 'Failed to fetch product', details: fetchError.message },
        { status: 500 }
      );
    }

    if (!product) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 });
    }

    // Cập nhật bot_id dựa trên action
    let updatedBotIds = Array.isArray(product.bot_id) ? [...product.bot_id] : [];

    if (action === 'add') {
      // Thêm bot_id nếu chưa tồn tại
      if (!updatedBotIds.includes(bot_id)) {
        updatedBotIds.push(bot_id);
      }
    } else if (action === 'remove') {
      // Xóa bot_id nếu tồn tại
      updatedBotIds = updatedBotIds.filter((id) => id !== bot_id);
    }

    // Cập nhật sản phẩm trong database
    // Đảm bảo bot_id được định dạng đúng là mảng PostgreSQL
    const { error: updateError } = await request.supabase
      .from('products')
      .update({ bot_id: updatedBotIds })
      .eq('id', product_id)
      .eq('tenant_id', request.tenant_id);

    if (updateError) {
      return NextResponse.json(
        { error: 'Failed to update product', details: updateError.message },
        { status: 500 }
      );
    }

    // Lấy URL Weaviate từ biến môi trường
    const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';

    // Gọi API Weaviate để cập nhật bot_id
    try {
      const response = await fetch(`${BACKEND_API_URL}/api/weaviate/products/update-bot-id`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${request.user.accessToken}`,
        },
        body: JSON.stringify({
          tenant_id: request.tenant_id,
          product_id,
          bot_id,
          action,
        }),
      });

      if (!response.ok) {
        console.warn('Warning: Failed to sync with Weaviate');
      }
    } catch (weaviateError) {
      console.error('Error syncing with Weaviate:', weaviateError);
      // Không ảnh hưởng đến kết quả trả về nếu đồng bộ Weaviate thất bại
    }

    return NextResponse.json({
      success: true,
      message: action === 'add' ? 'Added bot_id to product' : 'Removed bot_id from product',
      data: { product_id, bot_id, action },
    });
  } catch (error) {
    console.error('Error updating bot_id:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
});
