import { NextResponse } from 'next/server';

/**
 * API route để xóa sản phẩm khỏi Weaviate dựa trên product_id
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - <PERSON><PERSON><PERSON> hồi HTTP
 */
export async function DELETE(request) {
  try {
    const { product_id, tenant_id } = await request.json();

    // Kiểm tra dữ liệu đầu vào
    if (!product_id || !tenant_id) {
      return NextResponse.json(
        { error: 'Missing required fields: product_id, tenant_id' },
        { status: 400 }
      );
    }

    // Lấy URL Weaviate từ biến môi trường
    const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';

    // Gọi API Weaviate
    const response = await fetch(`${BACKEND_API_URL}/api/weaviate/products/delete-by-id`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        product_id,
        tenant_id,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { error: errorData.message || 'Failed to delete product by ID' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in delete-by-id API route:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
}
