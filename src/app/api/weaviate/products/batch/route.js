import { NextResponse } from 'next/server';

/**
 * API route để thêm nhiều sản phẩm vào Weaviate
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - <PERSON><PERSON><PERSON> hồi HTTP
 */
export async function POST(request) {
  try {
    const { products } = await request.json();

    // Kiểm tra dữ liệu đầu vào
    if (!products || !Array.isArray(products) || products.length === 0) {
      return NextResponse.json({ message: 'No products to add' }, { status: 200 });
    }

    // Lấy URL Weaviate từ biến môi trường
    const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';

    // Gọi API Weaviate
    const response = await fetch(`${BACKEND_API_URL}/api/weaviate/products/batch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ products }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { error: errorData.message || 'Failed to add products' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in batch API route:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
}
