import { NextResponse } from 'next/server';

import { withTenantAuth, validateTenantId } from 'src/utils/server-auth';

/**
 * API route để cập nhật trạng thái kích hoạt của sản phẩm trong Weaviate
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const PUT = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Validate request body và kiểm tra tenant_id
    const validation = await validateTenantId(request, request.tenant_id);
    if (!validation.valid) {
      return NextResponse.json({ error: validation.error }, { status: validation.status });
    }

    const { is_active, product_id } = validation.body;

    // Kiểm tra dữ liệu đầu vào
    if (is_active === undefined || !product_id) {
      return NextResponse.json(
        { error: 'Missing required fields: is_active, product_id' },
        { status: 400 }
      );
    }

    // Lấy URL Weaviate từ biến môi trường
    const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';

    // Gọi API Weaviate
    const response = await fetch(`${BACKEND_API_URL}/api/weaviate/products/toggle-active`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${request.user.accessToken}`,
      },
      body: JSON.stringify({
        tenant_id: request.tenant_id, // Sử dụng tenant_id từ xác thực
        is_active,
        product_id,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { error: errorData.message || 'Failed to toggle product active status' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in toggle-active API route:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
});
