import { NextResponse } from 'next/server';

/**
 * API route để cập nhật sản phẩm trong Weaviate
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - <PERSON><PERSON><PERSON> hồi HTTP
 */
export async function PUT(request) {
  try {
    const { data } = await request.json();

    // Kiểm tra dữ liệu đầu vào
    if (!data) {
      return NextResponse.json({ error: 'Missing required field: data' }, { status: 400 });
    }

    // Lấy URL Weaviate từ biến môi trường
    const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';

    // Gọi API Weaviate
    const response = await fetch(`${BACKEND_API_URL}/api/weaviate/products/update`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ data }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { error: errorData.message || 'Failed to update product' },
        { status: response.status }
      );
    }

    const responseData = await response.json();
    return NextResponse.json(responseData);
  } catch (error) {
    console.error('Error in update API route:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
}
