import { NextResponse } from 'next/server';

import { createClient } from 'src/utils/supabase/server';
import { verifyWebhookData } from 'src/utils/payos-utils';

import { PAYMENT_STATUSES } from 'src/actions/mooly-chatbot/credit-constants';
import { updatePaymentStatus } from 'src/actions/mooly-chatbot/credit-service';

/**
 * Xử lý webhook từ PayOS
 * @param {Request} request - Request object
 * @returns {Promise<Response>} - Response object
 */
export async function POST(request) {
  try {
    // Lấy dữ liệu từ request
    const webhookData = await request.json();

    // Xác minh dữ liệu webhook
    const { success, data, error } = verifyWebhookData(webhookData);

    if (!success) {
      return NextResponse.json(
        { success: false, message: error },
        { status: 400 }
      );
    }

    // Kết nối với Supabase
    const supabase = await createClient();

    // <PERSON><PERSON><PERSON> thông tin thanh toán từ orderCode
    // Trong webhook mới của PayOS, orderCode nằm trong data.orderCode
    const orderCode = data.orderCode || data.order_code || '';

    if (!orderCode) {
      return NextResponse.json(
        { success: false, message: 'Invalid order code in webhook data' },
        { status: 400 }
      );
    }

    // Tìm thanh toán theo order_code
    let { data: paymentData, error: paymentError } = await supabase
      .from('credit_payments')
      .select('*')
      .eq('order_code', orderCode)
      .single();

    // Nếu không tìm thấy, thử tìm theo payment_id (cho tương thích ngược)
    if (paymentError || !paymentData) {
      const { data: paymentByPaymentId, error: paymentIdError } = await supabase
        .from('credit_payments')
        .select('*')
        .eq('payment_id', orderCode)
        .single();

      if (!paymentIdError && paymentByPaymentId) {
        paymentData = paymentByPaymentId;
        paymentError = null;
      } else {
        // Thử tìm trong payment_data
        const { data: payments, error: paymentsError } = await supabase
          .from('credit_payments')
          .select('*')
          .eq('payment_status', PAYMENT_STATUSES.PENDING);

        if (!paymentsError && payments && payments.length > 0) {
          // Tìm payment có chứa orderCode trong payment_data
          for (const payment of payments) {
            if (payment.payment_data &&
                (payment.payment_data.orderCode === orderCode ||
                 payment.payment_data.order_code === orderCode)) {
              paymentData = payment;
              paymentError = null;
              break;
            }
          }
        }
      }
    }

    if (paymentError || !paymentData) {
      return NextResponse.json(
        { success: false, message: 'Payment not found' },
        { status: 404 }
      );
    }

    // Kiểm tra trạng thái thanh toán
    // Trong webhook mới của PayOS, mã thành công là '00'
    if (data.code === '00') {
      // Thanh toán thành công
      // Kiểm tra xem payment đã được xử lý chưa
      if (paymentData.payment_status === PAYMENT_STATUSES.COMPLETED) {
        return NextResponse.json({
          success: true,
          message: 'Payment already processed'
        });
      }

      // Sử dụng hàm updatePaymentStatus để cập nhật trạng thái và xử lý transaction
      const updateResult = await updatePaymentStatus(
        paymentData.id,
        PAYMENT_STATUSES.COMPLETED,
        webhookData.data
      );

      if (!updateResult.success) {
        return NextResponse.json(
          { success: false, message: updateResult.error },
          { status: 500 }
        );
      }
    } else {
      // Thanh toán thất bại
      const updateResult = await updatePaymentStatus(
        paymentData.id,
        PAYMENT_STATUSES.FAILED,
        webhookData.data
      );

      if (!updateResult.success) {
        return NextResponse.json(
          { success: false, message: updateResult.error },
          { status: 500 }
        );
      }
    }

    return NextResponse.json({ success: true, message: 'Webhook processed successfully' });
  } catch (error) {
    console.error('PayOS webhook error:', error);
    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    );
  }
}
