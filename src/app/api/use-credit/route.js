import { NextResponse } from 'next/server';

import { withTenantAuth } from 'src/utils/server-auth';
import { createClient } from 'src/utils/supabase/server';

import { CREDIT_TRANSACTION_TYPES } from 'src/actions/mooly-chatbot/credit-constants';

/**
 * API endpoint để sử dụng credit khi chatbot trả lời
 * @param {Request} request - Request object
 * @returns {Promise<Response>} - Response object
 */
export const POST = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Lấy dữ liệu từ request body
    const { amount, description, referenceId, referenceType } = await request.json();

    // Kiểm tra dữ liệu đầu vào
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { success: false, error: 'Amount must be a positive number' },
        { status: 400 }
      );
    }

    // Kết nối với Supabase
    const supabase = await createClient();

    // L<PERSON><PERSON> số dư hiện tại của tenant
    const { data: tenantCredit, error: creditError } = await supabase
      .from('tenant_credits')
      .select('*')
      .eq('tenant_id', tenantId)
      .single();

    if (creditError && creditError.code !== 'PGRST116') {
      // PGRST116 là lỗi "không tìm thấy", có thể chấp nhận được
      return NextResponse.json(
        { success: false, error: creditError.message },
        { status: 500 }
      );
    }

    // Nếu tenant chưa có bản ghi credit, tạo mới với số dư 0
    const currentBalance = tenantCredit ? tenantCredit.balance : 0;

    // Kiểm tra xem số dư có đủ không
    if (parseFloat(currentBalance) < parseFloat(amount)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient credit balance', data: { balance: currentBalance } },
        { status: 400 }
      );
    }

    // Tính toán số dư mới
    const newBalance = parseFloat(currentBalance) - parseFloat(amount);

    // Tạo transaction
    const { data: transaction, error: transactionError } = await supabase
      .from('credit_transactions')
      .insert({
        tenant_id: tenantId,
        amount: -amount, // Số âm vì đây là giao dịch trừ credit
        balance_before: currentBalance,
        balance_after: newBalance,
        type: CREDIT_TRANSACTION_TYPES.USAGE,
        description: description || 'Sử dụng chatbot',
        reference_id: referenceId,
        reference_type: referenceType,
        created_by: userId,
      })
      .select()
      .single();

    if (transactionError) {
      return NextResponse.json(
        { success: false, error: transactionError.message },
        { status: 500 }
      );
    }

    // Cập nhật số dư của tenant
    if (tenantCredit) {
      // Cập nhật số dư hiện tại
      const { error: updateError } = await supabase
        .from('tenant_credits')
        .update({
          balance: newBalance,
          last_updated: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', tenantCredit.id);

      if (updateError) {
        return NextResponse.json(
          { success: false, error: updateError.message },
          { status: 500 }
        );
      }
    } else {
      // Tạo mới bản ghi credit cho tenant
      const { error: createError } = await supabase.from('tenant_credits').insert({
        tenant_id: tenantId,
        balance: newBalance,
        last_updated: new Date().toISOString(),
      });

      if (createError) {
        return NextResponse.json(
          { success: false, error: createError.message },
          { status: 500 }
        );
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        transaction,
        balance: newBalance,
      },
    });
  } catch (error) {
    console.error('Error using credit:', error);
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    );
  }
});
