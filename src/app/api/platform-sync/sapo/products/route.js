import { NextResponse } from 'next/server';

import { withTenantAuth } from 'src/utils/server-auth';

/**
 * API route để đồng bộ sản phẩm từ Sapo
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const POST = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Lấy dữ liệu từ request body
    const { sapo_url, limit = 50, full_sync = true } = await request.json();

    // Kiểm tra dữ liệu đầu vào
    if (!sapo_url) {
      return NextResponse.json({ success: false, error: 'Sapo URL is required' }, { status: 400 });
    }

    // Lấy URL API từ biến môi trường
    const SYNC_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';

    // Gọi API đồng bộ sản phẩm từ Sapo
    const response = await fetch(`${SYNC_API_URL}/api/platform-sync/sapo/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${request.user.accessToken}`,
        tenant_id: request.tenant_id,
      },
      body: JSON.stringify({
        sapo_url,
        limit,
        full_sync,
      }),
    });

    // Xử lý phản hồi từ API
    const responseData = await response.json();

    if (!responseData.success) {
      return NextResponse.json(
        {
          success: false,
          error: responseData.message || 'Failed to sync products from Sapo',
        },
        { status: response.status }
      );
    }

    // Trả về kết quả thành công
    return NextResponse.json({
      success: true,
      message: responseData?.message,
      data: responseData,
    });
  } catch (error) {
    console.error('Error syncing products from Sapo:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Internal server error',
      },
      { status: 500 }
    );
  }
});
