'use client';

import { useState } from 'react';
import { useBoolean } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import { alpha, useTheme } from '@mui/material/styles';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { Markdown } from 'src/components/markdown';
import { EmptyContent } from 'src/components/empty-content';

import ChatbotInstructionDialog from './chatbot-instruction-dialog';

// ----------------------------------------------------------------------

export default function ChatbotInstructionTab({ chatbot }) {
  const theme = useTheme();
  const [isTraining, setIsTraining] = useState(false);
  const [trainingResult, setTrainingResult] = useState(null);

  const instructionDialog = useBoolean();

  // Kiểm tra xem chatbot có dữ liệu training hay không
  const hasTrainingData = chatbot?.trainingData?.content;
  // Kiểm tra xem chatbot có instruction hay không (để biết đã training chưa)
  const hasInstruction = chatbot?.instruction || chatbot?.trainingData?.content;

  // Xử lý khi bắt đầu training
  const handleStartTraining = async (instructionData) => {
    try {
      setIsTraining(true);

      // Gọi API để training chatbot
      const response = await fetch('/api/train-chatbot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          instruction: instructionData.instruction,
        }),
      });

      // Lấy dữ liệu phản hồi từ API
      const result = await response.json();

      // Kiểm tra nếu không thành công
      if (!response.ok) {
        // Kiểm tra cụ thể lỗi không đủ credit
        if (result.error && result.error.includes('Số dư credit không đủ')) {
          // Hiển thị thông báo lỗi cụ thể về credit
          const currentBalance = result.data?.balance || 0;
          toast.error(
            <Stack spacing={1}>
              <Typography variant="subtitle2">Không đủ credit để training chatbot</Typography>
              <Typography variant="body2">
                Số dư hiện tại: <strong>{currentBalance} credit</strong>
              </Typography>
              <Typography variant="body2">
                Vui lòng nạp thêm credit để tiếp tục sử dụng dịch vụ.
              </Typography>
            </Stack>,
            { duration: 6000 } // Hiển thị lâu hơn để người dùng có thời gian đọc
          );
          throw new Error('Không đủ credit để training chatbot');
        } else {
          // Các lỗi khác
          throw new Error(result.error || 'Có lỗi xảy ra khi training chatbot');
        }
      }

      if (result.success) {
        setTrainingResult(result.data);

        // Hiển thị thông tin về credit đã sử dụng
        if (result.credit) {
          toast.success(
            <Stack spacing={1}>
              <Typography variant="subtitle2">Training chatbot thành công!</Typography>
              <Typography variant="body2">
                Đã sử dụng: <strong>{result.credit.used} credit</strong>
              </Typography>
              <Typography variant="body2">
                Số dư còn lại: <strong>{result.credit.balance} credit</strong>
              </Typography>
            </Stack>
          );
        }

        // Lưu kết quả training vào database
        if (chatbot?.id) {
          try {
            // Import service để cập nhật chatbot
            const { updateChatbot } = await import('src/actions/mooly-chatbot/chatbot-service');

            // Cập nhật chatbot với instruction là kết quả trả về từ API
            // và training_data là nội dung người dùng nhập vào
            const updateResult = await updateChatbot(chatbot.id, {
              instruction: result.data.instruction, // Lưu prompt được trả về vào field instruction
              trainingData: {
                ...chatbot.trainingData, // Giữ lại dữ liệu training cũ nếu có
                content: instructionData.instruction, // Lưu nội dung người dùng nhập vào
                lastTrainedAt: new Date().toISOString(),
              },
            });

            if (!updateResult.success) {
              console.error('Error updating chatbot:', updateResult.error);
              toast.warning('Training thành công nhưng không thể lưu kết quả');
            }
          } catch (updateError) {
            console.error('Error updating chatbot:', updateError);
            toast.warning('Training thành công nhưng không thể lưu kết quả');
          }
        } else {
          toast.warning('Không thể lưu kết quả vì không tìm thấy ID chatbot');
        }

        // Đợi thêm 1 giây để hiển thị tiến trình hoàn thành 100% trước khi đóng dialog
        setTimeout(() => {
          instructionDialog.onFalse(); // Đóng dialog sau khi training thành công
        }, 1000);
      } else {
        throw new Error(result.message || 'Training không thành công');
      }
    } catch (error) {
      console.error('Error training chatbot:', error);
      // Thông báo lỗi đã được xử lý ở trên, không cần hiển thị lại
      if (!error.message.includes('Không đủ credit')) {
        toast.error(error.message || 'Có lỗi xảy ra khi training chatbot');
      }
    } finally {
      // Đợi thêm 1 giây để hiển thị tiến trình hoàn thành 100% trước khi đặt isTraining = false
      setTimeout(() => {
        setIsTraining(false);
      }, 1000);
    }
  };

  // Hiển thị nội dung khi không có dữ liệu training
  if (!hasTrainingData && !trainingResult) {
    return (
      <Stack spacing={3}>
        <Card sx={{ p: 3 }}>
          <EmptyContent
            title="Chưa có hướng dẫn"
            description="Thêm hướng dẫn để chatbot có thể hiểu rõ hơn về nhiệm vụ và cách thức hoạt động"
            sx={{ py: 5 }}
            action={
              <Button
                variant="contained"
                startIcon={<Iconify icon="eva:plus-fill" />}
                onClick={instructionDialog.onTrue}
                sx={{
                  mt: 2,
                  px: 3,
                  py: 1.2,
                  borderRadius: 8,
                  boxShadow: theme.customShadows.primary,
                  bgcolor: theme.palette.primary.main,
                  '&:hover': {
                    bgcolor: theme.palette.primary.dark,
                    transform: 'translateY(-2px)',
                    transition: 'all 0.3s',
                  },
                }}
              >
                Thêm hướng dẫn
              </Button>
            }
          />
        </Card>

        <ChatbotInstructionDialog
          open={instructionDialog.value}
          onClose={instructionDialog.onFalse}
          onSubmit={handleStartTraining}
          isTraining={isTraining}
          chatbot={chatbot}
          initialValue={chatbot?.trainingData?.content || ''}
          isRetraining={hasInstruction}
        />
      </Stack>
    );
  }

  return (
    <Stack spacing={3}>
      {/* Hiển thị kết quả training nếu có */}
      {trainingResult && (
        <Card sx={{ p: 3 }}>
          <Stack spacing={2}>
            <Box sx={{
              p: 2,
              bgcolor: alpha(theme.palette.success.main, 0.08),
              borderRadius: 1,
              border: `1px solid ${alpha(theme.palette.success.main, 0.24)}`
            }}>
              <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
                <Iconify icon="eva:checkmark-circle-fill" width={24} height={24} color="success.main" />
                <Typography variant="h6" color="success.main">
                  Training thành công!
                </Typography>
              </Stack>
              <Typography variant="body2" color="text.secondary">
                Chatbot đã được training thành công và sẵn sàng sử dụng. Bạn có thể kiểm tra kết quả bên dưới.
              </Typography>
            </Box>

            <Typography variant="subtitle1">Nội dung đã nhập</Typography>
            <Box sx={{ p: 2, bgcolor: 'background.neutral', borderRadius: 1 }}>
              <Markdown>{trainingResult.content}</Markdown>
            </Box>

            {/* <Typography variant="subtitle1" sx={{ pt: 1 }}>Chi tiết kết quả training</Typography>
            <Box
              sx={{
                p: 2,
                bgcolor: alpha(theme.palette.info.main, 0.04),
                borderRadius: 1,
                border: `1px dashed ${alpha(theme.palette.info.main, 0.24)}`
              }}
            >
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Dưới đây là thông tin chi tiết về kết quả training. Những thông tin này không được hiển thị cho người dùng cuối, chỉ dùng để tham khảo.
              </Typography>

              <Accordion sx={{ bgcolor: 'background.neutral', boxShadow: 'none', '&:before': { display: 'none' } }}>
                <AccordionSummary
                  expandIcon={<Iconify icon="eva:arrow-down-fill" />}
                  sx={{ px: 2, py: 0 }}
                >
                  <Typography variant="subtitle2">Xem chi tiết</Typography>
                </AccordionSummary>
                <AccordionDetails sx={{ px: 2, pt: 0 }}>
                  <Stack spacing={2}>
                    {trainingResult.context && (
                      <Box>
                        <Typography variant="subtitle2" sx={{ mb: 1 }}>Context</Typography>
                        <Typography variant="body2" sx={{ color: 'text.secondary', whiteSpace: 'pre-wrap' }}>
                          {trainingResult.context}
                        </Typography>
                      </Box>
                    )}

                    <Box>
                      <Typography variant="subtitle2" sx={{ mb: 1 }}>Instruction</Typography>
                      <Typography variant="body2" sx={{ color: 'text.secondary', whiteSpace: 'pre-wrap' }}>
                        {trainingResult.instruction}
                      </Typography>
                    </Box>

                    {trainingResult.faqs && trainingResult.faqs.length > 0 && (
                      <Box>
                        <Typography variant="subtitle2" sx={{ mb: 1 }}>FAQs</Typography>
                        <Stack spacing={1}>
                          {trainingResult.faqs.map((faq, index) => (
                            <Box key={index} sx={{ p: 1, bgcolor: alpha(theme.palette.primary.main, 0.08), borderRadius: 1 }}>
                              <Typography variant="subtitle2">{faq.topic}</Typography>
                              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                {faq.content}
                              </Typography>
                            </Box>
                          ))}
                        </Stack>
                      </Box>
                    )}
                  </Stack>
                </AccordionDetails>
              </Accordion>
            </Box> */}
          </Stack>
        </Card>
      )}

      {/* Hiển thị hướng dẫn hiện tại nếu có */}
      {hasTrainingData && (
        <Card sx={{ p: 3 }}>
          <Stack spacing={2}>
            <Typography variant="h6">Hướng dẫn hiện tại</Typography>
            <Box sx={{ p: 2, bgcolor: 'background.neutral', borderRadius: 1 }}>
              <Markdown>{chatbot.trainingData.content}</Markdown>
            </Box>
            {hasInstruction && (
              <Box sx={{
                p: 2,
                bgcolor: alpha(theme.palette.success.main, 0.08),
                borderRadius: 1,
                border: `1px solid ${alpha(theme.palette.success.main, 0.24)}`
              }}>
                <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
                  <Iconify icon="eva:checkmark-circle-fill" color="success.main" />
                  <Typography variant="subtitle2" color="success.main">
                    Đã training thành công
                  </Typography>
                </Stack>
                <Typography variant="body2" color="text.secondary">
                  Chatbot đã được training và sẵn sàng sử dụng. Bạn có thể training lại bất cứ lúc nào nếu muốn cập nhật hướng dẫn.
                </Typography>
              </Box>
            )}
          </Stack>
        </Card>
      )}

      {/* Nút để mở dialog thêm/chỉnh sửa hướng dẫn */}
      <Stack direction="row" justifyContent="center">
        <Button
          variant="contained"
          startIcon={<Iconify icon={hasTrainingData ? (hasInstruction ? 'solar:refresh-bold' : 'eva:edit-fill') : 'eva:plus-fill'} />}
          onClick={instructionDialog.onTrue}
          sx={{
            px: 3,
            py: 1.2,
            borderRadius: 8,
            boxShadow: theme.customShadows.primary,
            bgcolor: theme.palette.primary.main,
            '&:hover': {
              bgcolor: theme.palette.primary.dark,
              transform: 'translateY(-2px)',
              transition: 'all 0.3s',
            },
          }}
        >
          {hasTrainingData
            ? (hasInstruction
              ? 'Training lại'
              : 'Chỉnh sửa hướng dẫn')
            : 'Thêm hướng dẫn'
          }
        </Button>
      </Stack>

      <ChatbotInstructionDialog
        open={instructionDialog.value}
        onClose={instructionDialog.onFalse}
        onSubmit={handleStartTraining}
        isTraining={isTraining}
        chatbot={chatbot}
        initialValue={chatbot?.trainingData?.content || ''}
        isRetraining={hasInstruction}
      />
    </Stack>
  );
}
