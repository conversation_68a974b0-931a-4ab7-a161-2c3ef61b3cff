'use client';

import { z } from 'zod';
import PropTypes from 'prop-types';
import { useForm } from 'react-hook-form';
import { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';

import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import Dialog from '@mui/material/Dialog';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import AlertTitle from '@mui/material/AlertTitle';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import { alpha, useTheme } from '@mui/material/styles';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import LinearProgress from '@mui/material/LinearProgress';

import { Iconify } from 'src/components/iconify';
import { Markdown } from 'src/components/markdown';
import { Form, Field } from 'src/components/hook-form';

// Schema cho form hướng dẫn
const InstructionSchema = z.object({
  instruction: z.string().min(10, 'Hướng dẫn phải có ít nhất 10 ký tự'),
});

// Giá trị mặc định cho form
const defaultValues = {
  instruction: '',
};

// ----------------------------------------------------------------------

export default function ChatbotInstructionDialog({
  open,
  onClose,
  onSubmit,
  isTraining,
  chatbot,
  initialValue = '',
  isRetraining = false,
}) {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [trainingProgress, setTrainingProgress] = useState(0);
  const [trainingTime, setTrainingTime] = useState(0);

  // Khởi tạo form với giá trị mặc định hoặc giá trị ban đầu
  const methods = useForm({
    resolver: zodResolver(InstructionSchema),
    defaultValues: {
      ...defaultValues,
      instruction: initialValue,
    },
  });

  const {
    reset,
    watch,
    handleSubmit,
    formState: { isSubmitting, errors },
  } = methods;

  // Lấy giá trị hiện tại của trường instruction để hiển thị preview
  const currentInstruction = watch('instruction');

  // Xử lý khi đóng dialog
  const handleClose = () => {
    reset();
    onClose();
  };

  // Xử lý khi thay đổi tab
  const handleChangeTab = (event, newValue) => {
    setTabValue(newValue);
  };

  // Xử lý khi submit form
  const handleFormSubmit = handleSubmit((data) => {
    setTrainingProgress(0);
    setTrainingTime(0);
    onSubmit(data);
  });

  // Cập nhật tiến trình training
  useEffect(() => {
    let timer;
    let progressTimer;

    if (isTraining) {
      // Cập nhật thời gian training mỗi giây
      timer = setInterval(() => {
        setTrainingTime((prevTime) => prevTime + 1);
      }, 1000);

      // Giả lập tiến trình training
      progressTimer = setInterval(() => {
        setTrainingProgress((prevProgress) => {
          // Tăng dần tiến trình đến 95% (để tránh hiển thị hoàn thành khi chưa thực sự hoàn thành)
          if (prevProgress < 95) {
            return prevProgress + (95 - prevProgress) * 0.1;
          }
          return prevProgress;
        });
      }, 500);
    } else if (trainingProgress > 0) {
      // Khi training hoàn thành, đặt tiến trình về 100%
      setTrainingProgress(100);
    }

    return () => {
      clearInterval(timer);
      clearInterval(progressTimer);
    };
  }, [isTraining, trainingProgress]);

  return (
    <Dialog
      fullWidth
      maxWidth="md"
      open={open}
      onClose={handleClose}
      PaperProps={{
        sx: { maxHeight: '90vh' },
      }}
    >
      <DialogTitle>
        {isRetraining
          ? 'Training lại chatbot'
          : (initialValue ? 'Chỉnh sửa hướng dẫn' : 'Thêm hướng dẫn cho chatbot')}
      </DialogTitle>

      <DialogContent dividers sx={{ pt: 1, pb: 0, border: 'none' }}>
        {!isTraining && (
          <Box sx={{ mb: 2 }}>
            <Alert
              severity="info"
              sx={{ mb: 1 }}
            >
              <AlertTitle>Thông tin về credit</AlertTitle>
              <Typography variant="body2">
                Mỗi lần training chatbot sẽ sử dụng <strong>1 credit</strong>. Vui lòng đảm bảo tài khoản của bạn có đủ credit trước khi thực hiện.
              </Typography>
            </Alert>
          </Box>
        )}

        {isTraining && (
          <Box sx={{ mb: 2 }}>
            <Alert
              severity="warning"
              sx={{ mb: 1 }}
            >
              <AlertTitle>Đang training chatbot</AlertTitle>
              <Typography variant="body2">
                Vui lòng không đóng trang hoặc tắt trình duyệt trong quá trình training để tránh mất dữ liệu.
              </Typography>
            </Alert>

            <Box sx={{ width: '100%', mb: 1 }}>
              <LinearProgress
                variant="determinate"
                value={trainingProgress}
                sx={{
                  height: 10,
                  borderRadius: 1,
                  bgcolor: alpha(theme.palette.primary.main, 0.12),
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 1,
                    bgcolor: theme.palette.primary.main,
                  }
                }}
              />
            </Box>

            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Typography variant="caption" color="text.secondary">
                Tiến trình: {Math.round(trainingProgress)}%
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Thời gian: {trainingTime} giây
              </Typography>
            </Stack>
          </Box>
        )}

        <Form methods={methods} onSubmit={handleFormSubmit}>
          <Stack spacing={3}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs value={tabValue} onChange={handleChangeTab}>
                <Tab
                  label="Soạn thảo"
                  icon={<Iconify icon="solar:pen-bold" />}
                  iconPosition="start"
                />
                <Tab
                  label="Xem trước"
                  icon={<Iconify icon="solar:eye-bold" />}
                  iconPosition="start"
                />
                <Tab
                  label="Hướng dẫn"
                  icon={<Iconify icon="solar:info-circle-bold" />}
                  iconPosition="start"
                />
              </Tabs>
            </Box>

            {tabValue === 0 && (
              <Field.Editor
                name="instruction"
                placeholder="Nhập hướng dẫn chi tiết cho chatbot..."
                sx={{ minHeight: 400 }}
                helperText={
                  errors.instruction
                    ? errors.instruction.message
                    : 'Nhập đầy đủ thông tin về chatbot, câu hỏi thường gặp, tính cách, nhiệm vụ, chính sách bán hàng...'
                }
              />
            )}

            {tabValue === 1 && (
              <Box sx={{ p: 2, bgcolor: 'background.neutral', borderRadius: 1, minHeight: 400, overflowY: 'auto' }}>
                {currentInstruction ? (
                  <Markdown>{currentInstruction}</Markdown>
                ) : (
                  <Typography variant="body2" sx={{ color: 'text.secondary', fontStyle: 'italic' }}>
                    Chưa có nội dung để xem trước
                  </Typography>
                )}
              </Box>
            )}

            {tabValue === 2 && (
              <Box sx={{ p: 2, bgcolor: alpha(theme.palette.info.main, 0.08), borderRadius: 1 }}>
                <Typography variant="h6" sx={{ mb: 2, color: theme.palette.info.main }}>
                  Hướng dẫn sử dụng
                </Typography>

                <Stack spacing={2}>
                  <Box>
                    <Typography variant="subtitle2">Cách viết hướng dẫn hiệu quả:</Typography>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Hãy cung cấp đầy đủ thông tin về chatbot, bao gồm:
                    </Typography>
                    <ul>
                      <li>
                        <Typography variant="body2">
                          <strong>Nhiệm vụ của chatbot:</strong> Mô tả rõ chatbot được tạo ra để làm gì
                        </Typography>
                      </li>
                      <li>
                        <Typography variant="body2">
                          <strong>Tính cách của chatbot:</strong> Thân thiện, chuyên nghiệp, vui vẻ...
                        </Typography>
                      </li>
                      <li>
                        <Typography variant="body2">
                          <strong>Thông tin về sản phẩm/dịch vụ:</strong> Mô tả chi tiết về sản phẩm, dịch vụ
                        </Typography>
                      </li>
                      <li>
                        <Typography variant="body2">
                          <strong>Chính sách bán hàng:</strong> Giá cả, khuyến mãi, bảo hành, đổi trả...
                        </Typography>
                      </li>
                      <li>
                        <Typography variant="body2">
                          <strong>Câu hỏi thường gặp:</strong> Liệt kê các câu hỏi và câu trả lời phổ biến
                        </Typography>
                      </li>
                    </ul>
                  </Box>

                  <Box>
                    <Typography variant="subtitle2">Định dạng Markdown:</Typography>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Bạn có thể sử dụng Markdown để định dạng nội dung:
                    </Typography>
                    <ul>
                      <li>
                        <Typography variant="body2" component="div">
                          <code># Tiêu đề lớn</code> - Tiêu đề cấp 1
                        </Typography>
                      </li>
                      <li>
                        <Typography variant="body2" component="div">
                          <code>## Tiêu đề nhỏ hơn</code> - Tiêu đề cấp 2
                        </Typography>
                      </li>
                      <li>
                        <Typography variant="body2" component="div">
                          <code>**In đậm**</code> - Văn bản in đậm
                        </Typography>
                      </li>
                      <li>
                        <Typography variant="body2" component="div">
                          <code>*In nghiêng*</code> - Văn bản in nghiêng
                        </Typography>
                      </li>
                      <li>
                        <Typography variant="body2" component="div">
                          <code>[Liên kết](URL)</code> - Tạo liên kết
                        </Typography>
                      </li>
                      <li>
                        <Typography variant="body2" component="div">
                          <code>![Mô tả](URL hình ảnh)</code> - Chèn hình ảnh
                        </Typography>
                      </li>
                      <li>
                        <Typography variant="body2" component="div">
                          <code>- Mục 1</code> - Danh sách không thứ tự
                        </Typography>
                      </li>
                    </ul>
                  </Box>

                  <Box>
                    <Typography variant="subtitle2">Video hướng dẫn:</Typography>
                    <Button
                      variant="outlined"
                      color="info"
                      startIcon={<Iconify icon="logos:youtube-icon" />}
                      href="https://www.youtube.com/watch?v=dQw4w9WgXcQ"
                      target="_blank"
                      sx={{ mt: 1 }}
                    >
                      Xem video hướng dẫn
                    </Button>
                  </Box>
                </Stack>
              </Box>
            )}
          </Stack>
        </Form>
      </DialogContent>

      <DialogActions>
        <Button variant="outlined" color="inherit" onClick={handleClose}>
          Hủy
        </Button>

        <LoadingButton
          type="submit"
          variant="contained"
          loading={isSubmitting || isTraining}
          onClick={handleFormSubmit}
          startIcon={<Iconify icon={isRetraining ? "solar:refresh-bold" : "solar:rocket-bold"} />}
          sx={{
            px: 2,
            py: 1,
            borderRadius: 8,
            boxShadow: theme.customShadows.primary,
            bgcolor: theme.palette.primary.main,
            '&:hover': {
              bgcolor: theme.palette.primary.dark,
            },
          }}
        >
          {isRetraining ? 'Training lại' : 'Bắt đầu Training'}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
}

ChatbotInstructionDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  isTraining: PropTypes.bool,
  chatbot: PropTypes.object,
  initialValue: PropTypes.string,
  isRetraining: PropTypes.bool,
};
