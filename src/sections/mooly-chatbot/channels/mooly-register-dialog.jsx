'use client';

import PropTypes from 'prop-types';
import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Alert from '@mui/material/Alert';
import Dialog from '@mui/material/Dialog';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import AlertTitle from '@mui/material/AlertTitle';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import CircularProgress from '@mui/material/CircularProgress';

import { toast } from 'src/components/snackbar';

import { useAuthContext } from 'src/auth/hooks';

// ----------------------------------------------------------------------

export default function MoolyRegisterDialog({
  open,
  onClose,
  onRegisterSuccess,
}) {
  const { user } = useAuthContext();
  const [isRegistering, setIsRegistering] = useState(false);
  const [registrationResult, setRegistrationResult] = useState(null);
  const [userInfo, setUserInfo] = useState(null);

  // Lấy thông tin user khi component được mount
  useEffect(() => {
    if (user) {
      // Phân tách tên đầy đủ thành họ và tên
      const fullName = user.displayName || '';
      const nameParts = fullName.split(' ');
      const lastName = nameParts.pop() || '';
      const firstName = nameParts.join(' ') || '';

      setUserInfo({
        firstName: firstName || 'User',
        lastName: lastName || user.id.substring(0, 5),
        email: user.email || '',
      });
    }
  }, [user]);

  // Xử lý đóng dialog
  const handleClose = () => {
    setRegistrationResult(null);
    onClose();
  };

  // Xử lý tạo tài khoản
  const handleRegister = async () => {
    if (!userInfo) return;

    try {
      setIsRegistering(true);

      // Gọi API để tạo tài khoản Mooly
      const response = await fetch('/api/mooly-proxy/create-chatwoot-account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          first_name: userInfo.firstName,
          last_name: userInfo.lastName,
          email: userInfo.email,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setRegistrationResult(result.data);
        toast.success('Khởi tạo tài khoản Mooly thành công!');
      } else {
        toast.error(`Lỗi: ${result.error}`);
      }
    } catch (error) {
      console.error(error);
      toast.error(`Lỗi: ${error.message}`);
    } finally {
      setIsRegistering(false);
    }
  };

  // Xử lý lưu thông tin tài khoản
  const handleSaveAccount = async () => {
    if (!registrationResult) return;

    try {
      const accountData = {
        userId: registrationResult.userId,
        accountId: registrationResult.accountId,
        token: registrationResult.token,
      };

      const result = await onRegisterSuccess(accountData);

      if (result.success) {
        handleClose();
      }
    } catch (error) {
      console.error(error);
      toast.error(`Lỗi khi lưu thông tin tài khoản: ${error.message}`);
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} fullWidth maxWidth="sm">
      <DialogTitle>Khởi tạo tài khoản Mooly</DialogTitle>

      {!userInfo ? (
        <DialogContent>
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 5 }}>
            <CircularProgress />
          </Box>
        </DialogContent>
      ) : !registrationResult ? (
        <>
          <DialogContent>
            <Alert severity="info">
              <AlertTitle>Khởi tạo tài khoản Mooly</AlertTitle>
              Bạn cần khởi tạo tài khoản Mooly để sử dụng tính năng quản lý channel chatbot.
            </Alert>
          </DialogContent>

          <DialogActions>
            <Button variant="outlined" color="inherit" onClick={handleClose}>
              Hủy
            </Button>

            <LoadingButton
              variant="contained"
              color="primary"
              onClick={handleRegister}
              loading={isRegistering}
            >
              Khởi tạo tài khoản
            </LoadingButton>
          </DialogActions>
        </>
      ) : (
        <DialogContent>
          <Alert severity="success" sx={{ mb: 3 }}>
            <AlertTitle>Khởi tạo thành công!</AlertTitle>
            Tài khoản Mooly của bạn đã được tạo thành công.
          </Alert>

          <Alert severity="info" sx={{ mb: 3 }}>
            <AlertTitle>Thông tin đăng nhập</AlertTitle>
            <Typography variant="body2" sx={{ mb: 1 }}>
              Bạn có thể đăng nhập vào hệ thống Mooly tại <a href="https://app.mooly.vn" target="_blank" rel="noopener noreferrer">app.mooly.vn</a> với thông tin sau:
            </Typography>
            <Typography variant="body2">
              <strong>Email:</strong> {userInfo?.email}<br />
              <strong>Mật khẩu:</strong> {registrationResult.defaultPassword}
            </Typography>
          </Alert>

          <DialogActions>
            <Button
              variant="contained"
              color="primary"
              onClick={handleSaveAccount}
            >
              Hoàn tất
            </Button>
          </DialogActions>
        </DialogContent>
      )}
    </Dialog>
  );
}

MoolyRegisterDialog.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  onRegisterSuccess: PropTypes.func,
};
