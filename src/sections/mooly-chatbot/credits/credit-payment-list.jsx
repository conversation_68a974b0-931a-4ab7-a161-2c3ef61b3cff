'use client';

import { useState } from 'react';
import PropTypes from 'prop-types';

import Chip from '@mui/material/Chip';
import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import Divider from '@mui/material/Divider';
import TableRow from '@mui/material/TableRow';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import CardHeader from '@mui/material/CardHeader';
import TableContainer from '@mui/material/TableContainer';

import { fDateTime } from 'src/utils/format-time';
import { fNumber, fCurrency } from 'src/utils/format-number';

import { PAYMENT_METHODS, PAYMENT_STATUSES } from 'src/actions/mooly-chatbot/credit-constants';

import {Scrollbar} from 'src/components/scrollbar';
import { emptyRows , TableNoData , TableEmptyRows, TableHeadCustom, TablePaginationCustom } from 'src/components/table';

// ----------------------------------------------------------------------

const TABLE_HEAD = [
  { id: 'createdAt', label: 'Thời gian', width: 160 },
  { id: 'creditAmount', label: 'Số credit', width: 120 },
  { id: 'amount', label: 'Số tiền', width: 120 },
  { id: 'paymentMethod', label: 'Phương thức', width: 140 },
  { id: 'paymentStatus', label: 'Trạng thái', width: 120 },
];

// ----------------------------------------------------------------------

export default function CreditPaymentList({ payments, loading }) {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setPage(0);
    setRowsPerPage(parseInt(event.target.value, 10));
  };

  const getPaymentMethodLabel = (method) => {
    switch (method) {
      case PAYMENT_METHODS.PAYOS:
        return 'PayOS';
      case PAYMENT_METHODS.BANK_TRANSFER:
        return 'Chuyển khoản';
      case PAYMENT_METHODS.MANUAL:
        return 'Thủ công';
      default:
        return method;
    }
  };

  const getPaymentStatusColor = (status) => {
    switch (status) {
      case PAYMENT_STATUSES.COMPLETED:
        return 'success';
      case PAYMENT_STATUSES.PENDING:
        return 'warning';
      case PAYMENT_STATUSES.FAILED:
        return 'error';
      case PAYMENT_STATUSES.REFUNDED:
        return 'info';
      default:
        return 'default';
    }
  };

  const getPaymentStatusLabel = (status) => {
    switch (status) {
      case PAYMENT_STATUSES.COMPLETED:
        return 'Hoàn thành';
      case PAYMENT_STATUSES.PENDING:
        return 'Đang xử lý';
      case PAYMENT_STATUSES.FAILED:
        return 'Thất bại';
      case PAYMENT_STATUSES.REFUNDED:
        return 'Đã hoàn tiền';
      default:
        return status;
    }
  };

  const dataFiltered = payments || [];
  const notFound = !dataFiltered.length;

  const denseHeight = 72;
  const currentPageData = dataFiltered.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
  const totalItems = dataFiltered.length;

  return (
    <Card>
      <CardHeader title="Lịch sử thanh toán" />

      <Divider />

      <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
        <Scrollbar>
          <Table size="small" sx={{ minWidth: 800 }}>
            <TableHeadCustom headCells={TABLE_HEAD} />

            <TableBody>
              {currentPageData.map((row) => (
                <TableRow key={row.id} hover>
                  <TableCell>{fDateTime(row.createdAt)}</TableCell>

                  <TableCell>{fNumber(row.creditAmount)}</TableCell>

                  <TableCell>{fCurrency(row.amount)} VNĐ</TableCell>

                  <TableCell>{getPaymentMethodLabel(row.paymentMethod)}</TableCell>

                  <TableCell>
                    <Chip
                      label={getPaymentStatusLabel(row.paymentStatus)}
                      color={getPaymentStatusColor(row.paymentStatus)}
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              ))}

              <TableEmptyRows
                height={denseHeight}
                emptyRows={emptyRows(page, rowsPerPage, totalItems)}
              />

              <TableNoData notFound={notFound} />
            </TableBody>
          </Table>
        </Scrollbar>
      </TableContainer>

      <TablePaginationCustom
        count={totalItems}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={[5, 10, 25]}
        dense
      />
    </Card>
  );
}

CreditPaymentList.propTypes = {
  payments: PropTypes.array,
  loading: PropTypes.bool,
};
