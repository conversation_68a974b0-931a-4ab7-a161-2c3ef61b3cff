'use client';

import PropTypes from 'prop-types';

import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';

import { fPercent, fCurrency, fNumber } from 'src/utils/format-number';

import {Label} from 'src/components/label';
import {Iconify} from 'src/components/iconify';

// ----------------------------------------------------------------------

export default function CreditPackageCard({ packageData, selected, onSelect }) {
  const { name, description, creditAmount, price, discountPercentage } = packageData;

  const hasDiscount = discountPercentage > 0;
  const originalPrice = price;
  const finalPrice = hasDiscount ? price * (1 - discountPercentage / 100) : price;

  return (
    <Card
      sx={{
        borderWidth: 2,
        borderColor: selected ? 'primary.main' : 'divider',
        borderStyle: 'solid',
        position: 'relative',
        transition: 'all 0.2s',
        '&:hover': {
          borderColor: 'primary.main',
          transform: 'translateY(-4px)',
          boxShadow: (theme) => theme.customShadows.z20,
        },
      }}
    >
      {hasDiscount && (
        <Label
          color="error"
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            zIndex: 9,
          }}
        >
          {`Giảm ${fPercent(discountPercentage / 100)}`}
        </Label>
      )}

      <CardContent>
        <Stack spacing={3}>
          <Stack spacing={1} alignItems="center" textAlign="center">
            <Typography variant="h5">{name}</Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {description}
            </Typography>
          </Stack>

          <Stack direction="row" justifyContent="center" alignItems="baseline" spacing={0.5}>
            <Typography variant="h3">{fCurrency(finalPrice)}</Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              VNĐ
            </Typography>
          </Stack>

          {hasDiscount && (
            <Stack direction="row" justifyContent="center" alignItems="center" spacing={1}>
              <Typography
                variant="body2"
                sx={{ color: 'text.disabled', textDecoration: 'line-through' }}
              >
                {fCurrency(originalPrice)} VNĐ
              </Typography>
              <Label color="error">{`Tiết kiệm ${fCurrency(originalPrice - finalPrice)}`}</Label>
            </Stack>
          )}

          <Divider sx={{ borderStyle: 'dashed' }} />

          <Stack spacing={2}>
            <Stack direction="row" alignItems="center" spacing={1.5}>
              <Iconify icon="eva:checkmark-circle-2-fill" sx={{ color: 'primary.main' }} />
              <Typography variant="body2">{`${fNumber(creditAmount)} credit`}</Typography>
            </Stack>

            <Stack direction="row" alignItems="center" spacing={1.5}>
              <Iconify icon="eva:checkmark-circle-2-fill" sx={{ color: 'primary.main' }} />
              <Typography variant="body2">Sử dụng không giới hạn thời gian</Typography>
            </Stack>

            <Stack direction="row" alignItems="center" spacing={1.5}>
              <Iconify icon="eva:checkmark-circle-2-fill" sx={{ color: 'primary.main' }} />
              <Typography variant="body2">Thanh toán an toàn</Typography>
            </Stack>
          </Stack>

          <Button
            fullWidth
            size="large"
            variant={selected ? 'contained' : 'outlined'}
            color={selected ? 'primary' : 'inherit'}
            onClick={() => onSelect(packageData.id)}
            startIcon={selected && <Iconify icon="eva:checkmark-fill" />}
          >
            {selected ? 'Đã chọn' : 'Chọn gói'}
          </Button>
        </Stack>
      </CardContent>
    </Card>
  );
}

CreditPackageCard.propTypes = {
  packageData: PropTypes.object.isRequired,
  selected: PropTypes.bool,
  onSelect: PropTypes.func,
};
