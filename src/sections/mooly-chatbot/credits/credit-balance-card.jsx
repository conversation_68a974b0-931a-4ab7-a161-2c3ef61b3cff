'use client';

import PropTypes from 'prop-types';
import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';
import LinearProgress from '@mui/material/LinearProgress';

import { useBoolean } from 'src/hooks/use-boolean';

import { fNumber } from 'src/utils/format-number';

import { Iconify } from 'src/components/iconify';

import CreditPurchaseDialog from './credit-purchase-dialog';

// ----------------------------------------------------------------------

export default function CreditBalanceCard({ balance, loading, onRefresh }) {
  const purchaseDialog = useBoolean();
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    // Tính toán tiến trình sử dụng credit dựa trên số dư thực tế
    // Giá trị mặc định cho maxCredit là 10000, nhưng có thể thay đổi tùy theo cấu hình
    const maxCredit = 10000; // Có thể lấy từ cấu hình hoặc tính toán dựa trên gói cao nhất
    const percentage = Math.min(100, (balance / maxCredit) * 100);
    setProgress(percentage);
  }, [balance]);

  return (
    <>
      <Card>
        <CardContent>
          <Stack spacing={3}>
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Typography variant="h6">Số dư Credit</Typography>
              <Button
                size="small"
                color="inherit"
                onClick={onRefresh}
                startIcon={<Iconify icon="eva:refresh-fill" />}
                disabled={loading}
              >
                Làm mới
              </Button>
            </Stack>

            <Stack direction="row" justifyContent="center" alignItems="center" spacing={2}>
              <Typography variant="h3">{fNumber(balance || 0)}</Typography>
              <Box sx={{ color: 'text.secondary' }}>credit</Box>
            </Stack>

            {/* <LinearProgress
              variant="determinate"
              value={progress}
              color={progress < 30 ? 'error' : 'primary'}
              sx={{ height: 8, borderRadius: 1 }}
            /> */}

            {/* <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                {progress < 30 ? 'Sắp hết credit' : 'Credit đang sử dụng'}
              </Typography>
              <Typography variant="body2">{`${Math.round(progress)}%`}</Typography>
            </Stack> */}

            <Divider sx={{ borderStyle: 'dashed' }} />

            <Button
              fullWidth
              size="large"
              color="primary"
              variant="contained"
              startIcon={<Iconify icon="eva:plus-fill" />}
              onClick={purchaseDialog.onTrue}
            >
              Mua thêm Credit
            </Button>
          </Stack>
        </CardContent>
      </Card>

      <CreditPurchaseDialog
        open={purchaseDialog.value}
        onClose={purchaseDialog.onFalse}
        onSuccess={onRefresh}
      />
    </>
  );
}

CreditBalanceCard.propTypes = {
  balance: PropTypes.number,
  loading: PropTypes.bool,
  onRefresh: PropTypes.func,
};
