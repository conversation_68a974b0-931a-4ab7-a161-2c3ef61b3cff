import { z as zod } from 'zod';

import { PAYMENT_METHODS } from 'src/actions/mooly-chatbot/credit-constants';

// ----------------------------------------------------------------------

// Schema validation cho form mua credit
export const CreditPurchaseSchema = zod.object({
  // ID gói credit đã chọn
  packageId: zod.string().min(1, { message: 'Vui lòng chọn gói credit!' }),

  // Phương thức thanh toán
  paymentMethod: zod.enum([
    PAYMENT_METHODS.PAYOS,
    PAYMENT_METHODS.BANK_TRANSFER,
    PAYMENT_METHODS.MANUAL,
  ]),
});

// Giá trị mặc định cho form mua credit
export const defaultCreditPurchaseValues = {
  packageId: '',
  paymentMethod: PAYMENT_METHODS.PAYOS,
};

// Schema validation cho form sử dụng credit
export const CreditUsageSchema = zod.object({
  // Số lượng credit sử dụng
  amount: zod.number().positive({ message: 'Số lượng credit phải lớn hơn 0!' }),

  // Mô tả việc sử dụng
  description: zod.string().min(1, { message: 'Vui lòng nhập mô tả!' }),
});

// Giá trị mặc định cho form sử dụng credit
export const defaultCreditUsageValues = {
  amount: 0,
  description: '',
};
