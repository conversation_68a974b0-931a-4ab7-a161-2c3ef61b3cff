'use client';

import PropTypes from 'prop-types';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Dialog from '@mui/material/Dialog';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import CircularProgress from '@mui/material/CircularProgress';

import { fNumber, fCurrency } from 'src/utils/format-number';

import { createPaymentLink } from 'src/actions/mooly-chatbot/payos-service';
import { PAYMENT_METHODS } from 'src/actions/mooly-chatbot/credit-constants';
import { getCreditPackages, createCreditPayment } from 'src/actions/mooly-chatbot/credit-service';

import { toast } from 'src/components/snackbar';
import { Form, RHFSelect } from 'src/components/hook-form';

import { useAuthContext } from 'src/auth/hooks';

import CreditPackageCard from './credit-package-card';
import { CreditPurchaseSchema, defaultCreditPurchaseValues } from './credit-schema';

// ----------------------------------------------------------------------

export default function CreditPurchaseDialog({ open, onClose, onSuccess }) {
  const { user } = useAuthContext();

  const [packages, setPackages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState(PAYMENT_METHODS.PAYOS);
  const [currentTab, setCurrentTab] = useState('packages');

  const methods = useForm({
    resolver: zodResolver(CreditPurchaseSchema),
    defaultValues: defaultCreditPurchaseValues,
  });

  const {
    reset,
    setValue,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const loadPackages = useCallback(async () => {
    try {
      setLoading(true);
      // Lấy danh sách gói credit không cần tenant_id
      // Bảng credit_packages đã được thêm vào EXCLUDED_TABLES trong tenant-middleware.js
      const { success, data, error } = await getCreditPackages({
        filters: { isActive: true },
        orderBy: 'sortOrder',
        ascending: true,
      });

      if (success) {
        setPackages(data);
      } else {
        toast.error(error.message || 'Không thể tải gói credit');
      }
    } catch (error) {
      console.error('Error loading credit packages:', error);
      toast.error('Đã xảy ra lỗi khi tải gói credit');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (open) {
      loadPackages();
    }
  }, [open, loadPackages]);

  useEffect(() => {
    if (selectedPackage) {
      setValue('packageId', selectedPackage);
    }
  }, [selectedPackage, setValue]);

  const handleSelectPackage = (packageId) => {
    setSelectedPackage(packageId);
  };

  const handleChangeTab = (_, newValue) => {
    setCurrentTab(newValue);
  };

  const handleChangePaymentMethod = (event) => {
    setPaymentMethod(event.target.value);
    setValue('paymentMethod', event.target.value);
  };

  const handleCloseDialog = () => {
    reset();
    setSelectedPackage(null);
    setCurrentTab('packages');
    onClose();
  };

  const onSubmit = async (data) => {
    try {
      // Tìm thông tin gói đã chọn
      const selectedPackageData = packages.find((pkg) => pkg.id === data.packageId);
      if (!selectedPackageData) {
        toast.error('Vui lòng chọn gói credit');
        return;
      }

      // Tính giá cuối cùng sau khi áp dụng giảm giá
      const finalPrice = selectedPackageData.price * (1 - selectedPackageData.discountPercentage / 100);

      // Tạo payment trong database
      // Không cần truyền tenantId vì supabase-utils sẽ tự động thêm tenant_id
      const paymentResult = await createCreditPayment({
        packageId: data.packageId,
        amount: finalPrice, // Sử dụng giá đã giảm
        creditAmount: selectedPackageData.creditAmount,
        paymentMethod: data.paymentMethod,
        createdBy: user?.id,
      });

      if (!paymentResult.success) {
        toast.error(paymentResult.error?.message || 'Không thể tạo thanh toán');
        return;
      }

      // Nếu thanh toán qua PayOS, tạo link thanh toán
      if (data.paymentMethod === PAYMENT_METHODS.PAYOS) {
        // Lấy order_code từ kết quả tạo payment
        const paymentId = paymentResult.data[0].id;
        let orderCode = paymentResult.data[0].orderCode;

        if (!orderCode) {
          console.warn('orderCode is null, fetching from database...');

          // Nếu orderCode là null, thử lấy lại từ database
          try {
            const response = await fetch(`/api/credits/payment/${paymentId}`);
            const result = await response.json();

            if (result.success && result.data && result.data.orderCode) {
              orderCode = result.data.orderCode;
              console.log(`Retrieved orderCode ${orderCode} from database`);
            } else {
              toast.error('Không thể tạo mã đơn hàng');
              return;
            }
          } catch (error) {
            console.error('Error fetching payment details:', error);
            toast.error('Không thể tạo mã đơn hàng');
            return;
          }
        }

        // Sử dụng thông tin người dùng hiện tại từ user context
        const paymentLinkResult = await createPaymentLink({
          orderCode, // Sử dụng order_code từ bảng credit_payments
          paymentId, // Thêm payment_id để cập nhật payment_data sau khi tạo link
          amount: finalPrice, // Sử dụng giá đã giảm
          description: `Mua ${selectedPackageData.creditAmount} credit`,
          buyerName: user?.displayName || user?.email?.split('@')[0] || 'Khách hàng',
          buyerEmail: user?.email || '',
          buyerPhone: '',
          returnUrl: `${window.location.origin}/dashboard/credits?status=success&payment_id=${paymentId}`,
          cancelUrl: `${window.location.origin}/dashboard/credits?status=cancel&payment_id=${paymentId}`,
          items: [
            {
              name: selectedPackageData.name,
              quantity: 1,
              price: finalPrice,
            },
          ],
        });

        if (paymentLinkResult.success) {
          // Mở link thanh toán trong cửa sổ mới
          window.open(paymentLinkResult.data.checkoutUrl, '_blank');
          handleCloseDialog();
          onSuccess?.();

          // Hiển thị thông báo thành công
          toast.success('Đang chuyển đến trang thanh toán...');
        } else {
          toast.error(paymentLinkResult.error || 'Không thể tạo link thanh toán');
        }
      } else {
        // Thanh toán bằng phương thức khác
        toast.success('Đã tạo yêu cầu thanh toán thành công');
        handleCloseDialog();
        onSuccess?.();
      }
    } catch (error) {
      console.error('Error creating payment:', error);
      toast.error('Đã xảy ra lỗi khi tạo thanh toán');
    }
  };

  const getSelectedPackageInfo = () => {
    if (!selectedPackage) return null;
    return packages.find((pkg) => pkg.id === selectedPackage);
  };

  const packageInfo = getSelectedPackageInfo();

  return (
    <Dialog fullWidth maxWidth="md" open={open} onClose={handleCloseDialog}>
      <DialogTitle sx={{ pb: 2 }}>Mua thêm Credit</DialogTitle>

      <Tabs
        value={currentTab}
        onChange={handleChangeTab}
        sx={{
          px: 3,
          boxShadow: (theme) => `inset 0 -1px 0 ${theme.palette.divider}`,
        }}
      >
        <Tab value="packages" label="Chọn gói Credit" />
        <Tab
          value="payment"
          label="Thanh toán"
          disabled={!selectedPackage}
          sx={{ '&.Mui-disabled': { opacity: 0.6 } }}
        />
      </Tabs>

      <DialogContent sx={{ mt: 2, px: 3, pb: 3 }}>
        {currentTab === 'packages' && (
          <>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', my: 5 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Grid container spacing={3}>
                {packages.map((pkg) => (
                  <Grid item size={{ xs: 12, md: 4 }} key={pkg.id}>
                    <CreditPackageCard
                      packageData={pkg}
                      selected={selectedPackage === pkg.id}
                      onSelect={handleSelectPackage}
                    />
                  </Grid>
                ))}
              </Grid>
            )}
          </>
        )}

        {currentTab === 'payment' && packageInfo && (
          <Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
            <Stack spacing={3}>
              <Box
                sx={{
                  p: 3,
                  borderRadius: 1,
                  bgcolor: 'background.neutral',
                }}
              >
                <Stack spacing={2.5}>
                  <Stack direction="row" justifyContent="space-between">
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Gói đã chọn
                    </Typography>
                    <Typography variant="subtitle2">{packageInfo.name}</Typography>
                  </Stack>

                  <Stack direction="row" justifyContent="space-between">
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Số lượng Credit
                    </Typography>
                    <Typography variant="subtitle2">{fNumber(packageInfo.creditAmount)}</Typography>
                  </Stack>

                  <Stack direction="row" justifyContent="space-between">
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Giá gốc
                    </Typography>
                    <Typography variant="subtitle2">{fCurrency(packageInfo.price)} VNĐ</Typography>
                  </Stack>

                  {packageInfo.discountPercentage > 0 && (
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                        Giảm giá
                      </Typography>
                      <Typography variant="subtitle2" sx={{ color: 'error.main' }}>
                        -{fCurrency((packageInfo.price * packageInfo.discountPercentage) / 100)} VNĐ
                      </Typography>
                    </Stack>
                  )}

                  <Divider sx={{ borderStyle: 'dashed' }} />

                  <Stack direction="row" justifyContent="space-between">
                    <Typography variant="subtitle1">Tổng thanh toán</Typography>
                    <Box sx={{ textAlign: 'right' }}>
                      <Typography variant="subtitle1" sx={{ color: 'primary.main' }}>
                        {fCurrency(
                          packageInfo.price * (1 - packageInfo.discountPercentage / 100)
                        )}{' '}
                        VNĐ
                      </Typography>
                      <Typography variant="caption" sx={{ fontStyle: 'italic' }}>
                        (Đã bao gồm VAT)
                      </Typography>
                    </Box>
                  </Stack>
                </Stack>
              </Box>

              <Box sx={{ bgcolor: 'background.neutral', p: 3, borderRadius: 1 }}>
                <Stack spacing={1.5}>
                  <Typography variant="subtitle2">Thông tin người mua</Typography>
                  <Typography variant="body2">
                    <strong>Tên:</strong> {user?.displayName || user?.email?.split('@')[0] || 'Khách hàng'}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Email:</strong> {user?.email || 'Chưa có thông tin'}
                  </Typography>
                  {/* <Typography variant="body2">
                    <strong>Tenant ID:</strong> {user?.tenant_id || 'Chưa có thông tin'}
                  </Typography> */}
                </Stack>
              </Box>

              {/* <Box sx={{ mt: 1 }}>
                <RHFSelect
                  name="paymentMethod"
                  label="Phương thức thanh toán"
                  value={paymentMethod}
                  onChange={handleChangePaymentMethod}
                >
                  <MenuItem value={PAYMENT_METHODS.PAYOS}>Thanh toán qua PayOS</MenuItem>
                  <MenuItem value={PAYMENT_METHODS.BANK_TRANSFER}>Chuyển khoản ngân hàng</MenuItem>
                  <MenuItem value={PAYMENT_METHODS.MANUAL}>Thanh toán thủ công</MenuItem>
                </RHFSelect>
              </Box> */}
            </Stack>
          </Form>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3, pt: 1, justifyContent: 'flex-end' }}>
        <Button variant="outlined" color="inherit" onClick={handleCloseDialog}>
          Hủy
        </Button>

        {currentTab === 'packages' && selectedPackage && (
          <Button variant="contained" onClick={() => setCurrentTab('payment')}>
            Tiếp tục
          </Button>
        )}

        {currentTab === 'payment' && (
          <Button
            type="submit"
            variant="contained"
            disabled={isSubmitting}
            onClick={handleSubmit(onSubmit)}
          >
            {isSubmitting ? 'Đang xử lý...' : 'Thanh toán'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
}

CreditPurchaseDialog.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  onSuccess: PropTypes.func,
};
