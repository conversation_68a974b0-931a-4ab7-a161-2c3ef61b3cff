'use client';

import { useState } from 'react';
import PropTypes from 'prop-types';

import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import Divider from '@mui/material/Divider';
import TableRow from '@mui/material/TableRow';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import Typography from '@mui/material/Typography';
import CardHeader from '@mui/material/CardHeader';
import TableContainer from '@mui/material/TableContainer';

import { fDateTime } from 'src/utils/format-time';
import { fNumber } from 'src/utils/format-number';

import { CREDIT_TRANSACTION_TYPES } from 'src/actions/mooly-chatbot/credit-constants';

import {Scrollbar} from 'src/components/scrollbar';
import { emptyRows , TableNoData , TableEmptyRows, TableHeadCustom, TablePaginationCustom } from 'src/components/table';

// ----------------------------------------------------------------------

const TABLE_HEAD = [
  { id: 'createdAt', label: 'Thời gian', width: 160 },
  { id: 'type', label: 'Loại giao dịch', width: 140 },
  { id: 'description', label: 'Mô tả', width: 200 },
  { id: 'amount', label: 'Số lượng', width: 120, align: 'right' },
  { id: 'balanceAfter', label: 'Số dư sau', width: 120, align: 'right' },
];

// ----------------------------------------------------------------------

export default function CreditTransactionList({ transactions, loading }) {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setPage(0);
    setRowsPerPage(parseInt(event.target.value, 10));
  };

  const getTransactionTypeLabel = (type) => {
    switch (type) {
      case CREDIT_TRANSACTION_TYPES.PURCHASE:
        return 'Mua credit';
      case CREDIT_TRANSACTION_TYPES.USAGE:
        return 'Sử dụng';
      case CREDIT_TRANSACTION_TYPES.REFUND:
        return 'Hoàn tiền';
      case CREDIT_TRANSACTION_TYPES.BONUS:
        return 'Thưởng';
      case CREDIT_TRANSACTION_TYPES.EXPIRATION:
        return 'Hết hạn';
      default:
        return type;
    }
  };

  const getTransactionTypeColor = (type) => {
    switch (type) {
      case CREDIT_TRANSACTION_TYPES.PURCHASE:
      case CREDIT_TRANSACTION_TYPES.BONUS:
        return 'success.main';
      case CREDIT_TRANSACTION_TYPES.USAGE:
      case CREDIT_TRANSACTION_TYPES.EXPIRATION:
        return 'error.main';
      case CREDIT_TRANSACTION_TYPES.REFUND:
        return 'info.main';
      default:
        return 'text.primary';
    }
  };

  const dataFiltered = transactions || [];
  const notFound = !dataFiltered.length;

  const denseHeight = 72;
  const currentPageData = dataFiltered.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
  const totalItems = dataFiltered.length;

  return (
    <Card>
      <CardHeader title="Lịch sử giao dịch" />

      <Divider />

      <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
        <Scrollbar>
          <Table size="small" sx={{ minWidth: 960 }}>
            <TableHeadCustom headCells={TABLE_HEAD} />

            <TableBody>
              {currentPageData.map((row) => (
                <TableRow key={row.id} hover>
                  <TableCell>{fDateTime(row.createdAt)}</TableCell>

                  <TableCell>
                    <Typography
                      variant="body2"
                      sx={{ color: getTransactionTypeColor(row.type) }}
                    >
                      {getTransactionTypeLabel(row.type)}
                    </Typography>
                  </TableCell>

                  <TableCell>{row.description}</TableCell>

                  <TableCell align="right">
                    <Typography
                      variant="body2"
                      sx={{
                        color:
                          row.amount > 0 ? 'success.main' : row.amount < 0 ? 'error.main' : 'text.primary',
                        fontWeight: 'bold',
                      }}
                    >
                      {row.amount > 0 ? '+' : ''}
                      {fNumber(row.amount)}
                    </Typography>
                  </TableCell>

                  <TableCell align="right">{fNumber(row.balanceAfter)}</TableCell>
                </TableRow>
              ))}

              <TableEmptyRows
                height={denseHeight}
                emptyRows={emptyRows(page, rowsPerPage, totalItems)}
              />

              <TableNoData notFound={notFound} />
            </TableBody>
          </Table>
        </Scrollbar>
      </TableContainer>

      <TablePaginationCustom
        count={totalItems}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={[5, 10, 25]}
        dense
      />
    </Card>
  );
}

CreditTransactionList.propTypes = {
  transactions: PropTypes.array,
  loading: PropTypes.bool,
};
