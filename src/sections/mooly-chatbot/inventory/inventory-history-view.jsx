'use client';

import { useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import TableBody from '@mui/material/TableBody';
import TextField from '@mui/material/TextField';
import IconButton from '@mui/material/IconButton';
import TableContainer from '@mui/material/TableContainer';
import InputAdornment from '@mui/material/InputAdornment';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import CircularProgress from '@mui/material/CircularProgress';

import { useInventory } from 'src/actions/mooly-chatbot/inventory-service';

import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import {
  useTable,
  emptyRows,
  TableNoData,
  getComparator,
  TableEmptyRows,
  TableHeadCustom,
  TablePaginationCustom,
} from 'src/components/table';

import InventoryHistoryFilters from './inventory-history-filters';
import InventoryHistoryTableRow from './inventory-history-table-row';

// ----------------------------------------------------------------------

const TABLE_HEAD = [
  { id: 'date', label: 'Thời gian', width: '15%' },
  { id: 'product', label: 'Sản phẩm', width: '25%' },
  { id: 'type', label: 'Loại giao dịch', width: '15%' },
  { id: 'quantity', label: 'Số lượng', align: 'center', width: '10%' },
  { id: 'previous_quantity', label: 'Trước', align: 'center', width: '10%' },
  { id: 'current_quantity', label: 'Sau', align: 'center', width: '10%' },
  { id: 'notes', label: 'Ghi chú', width: '15%' },
];

// ----------------------------------------------------------------------

export default function InventoryHistoryView() {
  const table = useTable({ defaultOrderBy: 'date', defaultOrder: 'desc' });
  const inventory = useInventory();

  const [tableData, setTableData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState({
    name: '',
    type: [],
    startDate: null,
    endDate: null,
  });

  // Hàm để tải dữ liệu lịch sử tồn kho
  const fetchInventoryHistory = useCallback(async () => {
    try {
      setIsLoading(true);

      // Gọi API để lấy dữ liệu lịch sử tồn kho
      const result = await inventory.getTransactions();
      if (result.success && result.data) {
        setTableData(result.data);
      } else {
        setTableData([]);
      }
    } catch (error) {
      console.error('Error fetching inventory history:', error);
      setTableData([]);
    } finally {
      setIsLoading(false);
    }
  }, [inventory]);

  // Tải dữ liệu khi component được mount
  useEffect(() => {
    fetchInventoryHistory();
  }, []);

  const FILTERS = [
    {
      id: 'type',
      label: 'Loại giao dịch',
      options: [
        { value: 'adjustment', label: 'Điều chỉnh' },
        { value: 'stock_in', label: 'Nhập kho' },
        { value: 'stock_out', label: 'Xuất kho' },
        { value: 'sale', label: 'Bán hàng' },
        { value: 'return', label: 'Trả hàng' },
        { value: 'initial', label: 'Khởi tạo' },
      ],
    },
  ];

  const dataFiltered = applyFilter({
    inputData: tableData,
    comparator: getComparator(table.order, table.orderBy),
    filters,
  });

  const denseHeight = table.dense ? 60 : 80;

  const canReset = !!filters.name || !!filters.type.length || !!filters.startDate || !!filters.endDate;

  const notFound = (!dataFiltered.length && canReset) || !dataFiltered.length;

  const handleFilters = useCallback(
    (name, value) => {
      table.onResetPage();
      setFilters((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    },
    [table]
  );

  const handleResetFilters = useCallback(() => {
    setFilters({
      name: '',
      type: [],
      startDate: null,
      endDate: null,
    });
  }, []);

  const handleRefresh = useCallback(() => {
    fetchInventoryHistory();
  }, [fetchInventoryHistory]);

  return (
    <Card>
      <Stack
        spacing={2}
        alignItems="center"
        direction={{
          xs: 'column',
          md: 'row',
        }}
        sx={{ px: 2.5, py: 2 }}
      >
        <TextField
          fullWidth
          value={filters.name}
          onChange={(e) => handleFilters('name', e.target.value)}
          placeholder="Tìm kiếm theo tên sản phẩm..."
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
              </InputAdornment>
            ),
          }}
        />

        <Stack direction="row" spacing={1} flexShrink={0}>
          <DatePicker
            label="Từ ngày"
            value={filters.startDate}
            onChange={(newValue) => {
              handleFilters('startDate', newValue);
            }}
            slotProps={{ textField: { fullWidth: true } }}
            sx={{ width: 180 }}
          />

          <DatePicker
            label="Đến ngày"
            value={filters.endDate}
            onChange={(newValue) => {
              handleFilters('endDate', newValue);
            }}
            slotProps={{ textField: { fullWidth: true } }}
            sx={{ width: 180 }}
          />

          <InventoryHistoryFilters
            filters={filters}
            onFilters={handleFilters}
            filterOptions={FILTERS}
          />

          <Tooltip title="Làm mới">
            <IconButton onClick={handleRefresh}>
              <Iconify icon="eva:refresh-outline" />
            </IconButton>
          </Tooltip>
        </Stack>
      </Stack>

      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          <TableContainer
            sx={{
              position: 'relative',
              overflow: 'unset',
              width: '100%',
              '& .MuiTable-root': {
                tableLayout: 'fixed',
              },
            }}
          >
            <Scrollbar>
              <Table
                size={table.dense ? 'small' : 'medium'}
                sx={{
                  minWidth: 960,
                  width: '100%',
                  borderCollapse: 'separate',
                  borderSpacing: '0',
                }}
              >
                <TableHeadCustom
                  order={table.order}
                  orderBy={table.orderBy}
                  headCells={TABLE_HEAD}
                  rowCount={tableData.length}
                  onSort={table.onSort}
                  sx={{
                    '& .MuiTableCell-root': {
                      bgcolor: (theme) => theme.palette.background.neutral,
                      borderBottom: (theme) => `solid 1px ${theme.palette.divider}`,
                    },
                  }}
                />

                <TableBody>
                  {dataFiltered
                    .slice(
                      table.page * table.rowsPerPage,
                      table.page * table.rowsPerPage + table.rowsPerPage
                    )
                    .map((row) => (
                      <InventoryHistoryTableRow key={row.id} row={row} />
                    ))}

                  <TableEmptyRows
                    height={denseHeight}
                    emptyRows={emptyRows(table.page, table.rowsPerPage, tableData.length)}
                  />

                  <TableNoData notFound={notFound} />
                </TableBody>
              </Table>
            </Scrollbar>
          </TableContainer>

          <TablePaginationCustom
            count={dataFiltered.length}
            page={table.page}
            rowsPerPage={table.rowsPerPage}
            onPageChange={table.onChangePage}
            onRowsPerPageChange={table.onChangeRowsPerPage}
            dense={table.dense}
            onChangeDense={table.onChangeDense}
          />
        </>
      )}
    </Card>
  );
}

// ----------------------------------------------------------------------

function applyFilter({ inputData, comparator, filters }) {
  const { name, type, startDate, endDate } = filters;

  const stabilizedThis = inputData.map((el, index) => [el, index]);

  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });

  inputData = stabilizedThis.map((el) => el[0]);

  if (name) {
    inputData = inputData.filter(
      (item) =>
        (item.productName && item.productName.toLowerCase().indexOf(name.toLowerCase()) !== -1) ||
        (item.variantName && item.variantName.toLowerCase().indexOf(name.toLowerCase()) !== -1)
    );
  }

  if (type.length) {
    inputData = inputData.filter((item) => type.includes(item.type));
  }

  if (startDate) {
    inputData = inputData.filter(
      (item) => new Date(item.createdAt) >= new Date(startDate)
    );
  }

  if (endDate) {
    // Đặt thời gian là cuối ngày
    const endOfDay = new Date(endDate);
    endOfDay.setHours(23, 59, 59, 999);

    inputData = inputData.filter(
      (item) => new Date(item.createdAt) <= endOfDay
    );
  }

  return inputData;
}
