'use client';

import PropTypes from 'prop-types';
import { useCallback } from 'react';

import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Badge from '@mui/material/Badge';
import Button from '@mui/material/Button';
import Drawer from '@mui/material/Drawer';
import Divider from '@mui/material/Divider';
import Tooltip from '@mui/material/Tooltip';
import Checkbox from '@mui/material/Checkbox';
import TextField from '@mui/material/TextField';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import FormControlLabel from '@mui/material/FormControlLabel';

import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';

// ----------------------------------------------------------------------

export default function InventoryHistoryFilters({
  open,
  onOpen,
  onClose,
  filters,
  onFilters,
  filterOptions,
}) {
  const handleFilterType = useCallback(
    (type) => {
      const checked = filters.type.includes(type)
        ? filters.type.filter((value) => value !== type)
        : [...filters.type, type];

      onFilters('type', checked);
    },
    [filters.type, onFilters]
  );

  const renderHead = (
    <Stack
      direction="row"
      alignItems="center"
      justifyContent="space-between"
      sx={{ py: 2, pr: 1, pl: 2.5 }}
    >
      <Typography variant="h6">Bộ lọc</Typography>

      <Tooltip title="Đóng">
        <IconButton onClick={onClose}>
          <Iconify icon="eva:close-fill" />
        </IconButton>
      </Tooltip>
    </Stack>
  );

  const renderTypeFilter = (
    <Stack>
      <Typography variant="subtitle2" sx={{ mb: 1 }}>
        Loại giao dịch
      </Typography>
      {filterOptions.find((option) => option.id === 'type')?.options.map((option) => (
        <FormControlLabel
          key={option.value}
          control={
            <Checkbox
              checked={filters.type.includes(option.value)}
              onClick={() => handleFilterType(option.value)}
            />
          }
          label={option.label}
        />
      ))}
    </Stack>
  );

  return (
    <>
      <Button
        disableRipple
        color="inherit"
        endIcon={
          <Badge
            color="error"
            variant="dot"
            invisible={!filters.type.length}
          >
            <Iconify icon="ic:round-filter-list" />
          </Badge>
        }
        onClick={onOpen}
      >
        Bộ lọc
      </Button>

      <Drawer
        anchor="right"
        open={open}
        onClose={onClose}
        slotProps={{
          backdrop: { invisible: true },
        }}
        PaperProps={{
          sx: { width: 280 },
        }}
      >
        {renderHead}

        <Divider />

        <Scrollbar sx={{ px: 2.5, py: 3 }}>
          <Stack spacing={3}>
            {renderTypeFilter}
          </Stack>
        </Scrollbar>

        <Box sx={{ p: 2.5 }}>
          <Button
            fullWidth
            size="large"
            color="inherit"
            variant="outlined"
            onClick={() => {
              onFilters('type', []);
            }}
            startIcon={<Iconify icon="eva:trash-2-outline" />}
          >
            Xóa bộ lọc
          </Button>
        </Box>
      </Drawer>

      <Stack direction="row" flexWrap="wrap" alignItems="center" sx={{ mt: 2 }}>
        {!!filters.type.length && (
          <Stack direction="row" flexWrap="wrap" alignItems="center" sx={{ mr: 1 }}>
            {filters.type.map((item) => {
              const label = filterOptions
                .find((option) => option.id === 'type')
                ?.options.find((option) => option.value === item)?.label;

              return (
                <Chip
                  key={item}
                  label={label}
                  size="small"
                  onDelete={() => handleFilterType(item)}
                  sx={{ m: 0.5 }}
                />
              );
            })}
          </Stack>
        )}
      </Stack>
    </>
  );
}

InventoryHistoryFilters.propTypes = {
  filters: PropTypes.object,
  onClose: PropTypes.func,
  onFilters: PropTypes.func,
  onOpen: PropTypes.func,
  open: PropTypes.bool,
  filterOptions: PropTypes.array,
};
