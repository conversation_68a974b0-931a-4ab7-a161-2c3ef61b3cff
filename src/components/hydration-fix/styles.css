/**
 * CSS để xử lý các vấn đề hydration
 * 
 * File này chứa các quy tắc CSS để xử lý các vấn đề hydration
 * liên quan đến các thuộc tính và class do browser extensions thêm vào.
 */

/* Ẩn các thuộc tính data-* liên quan đến extensions */
[data-atm-ext-installed],
[data-atm-ext-version],
[data-extension-id],
[data-extension-version] {
  content: none !important;
}

/* Vô hiệu hóa các class do extensions thêm vào */
.ext-installed,
.ext-active {
  all: unset !important;
}

/* Đảm bảo body không bị ảnh hưởng bởi các thuộc tính không mong muốn */
body {
  transition: none !important;
}

/* Đ<PERSON>m bảo các thuộc tính không mong muốn không ảnh hưởng đến layout */
html[data-atm-ext-installed],
html[data-atm-ext-version],
html[data-extension-id],
html[data-extension-version],
body[data-atm-ext-installed],
body[data-atm-ext-version],
body[data-extension-id],
body[data-extension-version] {
  all: revert !important;
}
