import { useId, useCallback } from 'react';

import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';

import { fNumber } from 'src/utils/format-number';

// ----------------------------------------------------------------------

export function CurrencyInput({
  sx,
  error,
  value,
  onChange,
  disabled,
  slotProps,
  helperText,
  placeholder = '0',
  currencySymbol = 'VND',
  min = 0,
  max = Number.MAX_SAFE_INTEGER,
  ...other
}) {
  const id = useId();

  // Xử lý khi người dùng nhập liệu
  const handleChange = useCallback(
    (event) => {
      // Lấy giá trị từ input
      const inputValue = event.target.value;

      // Loại bỏ tất cả các ký tự không phải số
      const numericValue = inputValue.replace(/[^\d]/g, '');

      // Chuyển đổi thành số
      const numberValue = numericValue ? parseInt(numericValue, 10) : 0;

      // Giới hạn giá trị trong khoảng min-max
      const clampedValue = Math.min(Math.max(numberValue, min), max);

      // Gọi callback onChange với giá trị đã xử lý
      onChange?.(event, clampedValue);
    },
    [max, min, onChange]
  );

  // Định dạng giá trị hiển thị
  const displayValue = value !== null && value !== undefined ? fNumber(value) : '';

  return (
    <TextField
      id={id}
      fullWidth
      value={displayValue}
      onChange={handleChange}
      disabled={disabled}
      placeholder={placeholder}
      error={!!error}
      helperText={error?.message ?? helperText}
      InputProps={{
        startAdornment: currencySymbol ? (
          <InputAdornment position="start">{currencySymbol}</InputAdornment>
        ) : null,
      }}
      sx={sx}
      {...other}
      slotProps={{
        ...slotProps,
        htmlInput: {
          autoComplete: 'off',
          inputMode: 'numeric',
          ...slotProps?.htmlInput,
        },
      }}
    />
  );
}

// ----------------------------------------------------------------------

export function transformCurrencyValue(value) {
  if (value === null || value === undefined || value === '') {
    return 0;
  }

  // Nếu là số, trả về nguyên giá trị
  if (typeof value === 'number') {
    return value;
  }

  // Nếu là chuỗi, loại bỏ tất cả ký tự không phải số và chuyển đổi thành số
  const numericValue = value.toString().replace(/[^\d]/g, '');
  return numericValue ? parseInt(numericValue, 10) : 0;
}
