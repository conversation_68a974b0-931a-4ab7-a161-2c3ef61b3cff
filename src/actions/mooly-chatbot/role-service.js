'use client';

import { useState } from 'react';

import {
  callRPC,
  fetchData,
  createData,
  updateData,
  deleteData,
  upsertData,
} from './supabase-utils';

// Tên bảng trong Supabase
const TABLE_NAME = 'roles';

/**
 * L<PERSON>y danh sách vai trò với các tùy chọn lọc
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getRoles(options = {}) {
  return fetchData(TABLE_NAME, options);
}

// Helper function để xử lý lỗi tham số đầu vào
const validateParam = (param, paramName) => {
  if (!param) {
    return { success: false, error: `${paramName} is required`, data: null };
  }
  return null;
};

/**
 * Lấy chi tiết một vai trò theo ID
 * @param {string} roleId - ID của vai trò
 * @returns {Promise<Object>} - Kế<PERSON> quả từ API
 */
export async function getRoleById(roleId) {
  const validationError = validateParam(roleId, 'Role ID');
  if (validationError) return validationError;

  return fetchData(TABLE_NAME, {
    filters: { id: roleId },
    single: true,
  });
}

/**
 * Lấy chi tiết một vai trò theo tên
 * @param {string} roleName - Tên của vai trò
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getRoleByName(roleName) {
  const validationError = validateParam(roleName, 'Role name');
  if (validationError) return validationError;

  return fetchData(TABLE_NAME, {
    filters: { name: roleName },
    single: true,
  });
}

/**
 * Tạo vai trò mới
 * @param {Object} roleData - Dữ liệu vai trò
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createRole(roleData) {
  return createData(TABLE_NAME, roleData);
}

/**
 * Cập nhật vai trò
 * @param {string} roleId - ID của vai trò
 * @param {Object} roleData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateRole(roleId, roleData) {
  const validationError = validateParam(roleId, 'Role ID');
  if (validationError) return validationError;

  return updateData(TABLE_NAME, roleData, { id: roleId });
}

/**
 * Xóa vai trò
 * @param {string} roleId - ID của vai trò
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteRole(roleId) {
  const validationError = validateParam(roleId, 'Role ID');
  if (validationError) return validationError;

  return deleteData(TABLE_NAME, { id: roleId });
}

/**
 * Tạo hoặc cập nhật vai trò
 * @param {Object} roleData - Dữ liệu vai trò
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertRole(roleData) {
  return upsertData(TABLE_NAME, roleData);
}

/**
 * Lấy danh sách quyền của vai trò
 * @param {string} roleId - ID của vai trò
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getRolePermissions(roleId) {
  const validationError = validateParam(roleId, 'Role ID');
  if (validationError) return validationError;

  return callRPC('get_role_permissions', { role_id_param: roleId });
}

/**
 * Gán quyền cho vai trò
 * @param {string} roleId - ID của vai trò
 * @param {string} permissionId - ID của quyền
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function assignPermissionToRole(roleId, permissionId) {
  const roleIdError = validateParam(roleId, 'Role ID');
  if (roleIdError) return roleIdError;

  const permissionIdError = validateParam(permissionId, 'Permission ID');
  if (permissionIdError) return permissionIdError;

  return callRPC('assign_permission_to_role', {
    role_id_param: roleId,
    permission_id_param: permissionId,
  });
}

/**
 * Xóa quyền của vai trò
 * @param {string} roleId - ID của vai trò
 * @param {string} permissionId - ID của quyền
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function removePermissionFromRole(roleId, permissionId) {
  const roleIdError = validateParam(roleId, 'Role ID');
  if (roleIdError) return roleIdError;

  const permissionIdError = validateParam(permissionId, 'Permission ID');
  if (permissionIdError) return permissionIdError;

  return callRPC('remove_permission_from_role', {
    role_id_param: roleId,
    permission_id_param: permissionId,
  });
}

/**
 * Hook để tạo, cập nhật, xóa vai trò
 * @returns {Object} - Các hàm mutation
 */
export function useRoleMutations() {
  const [loadingStates, setLoadingStates] = useState({
    creating: false,
    updating: false,
    deleting: false,
    upserting: false,
    assigningPermission: false,
    removingPermission: false,
  });

  // Helper function to handle loading state and error handling
  const withLoadingState = async (stateKey, asyncFn) => {
    setLoadingStates((prev) => ({ ...prev, [stateKey]: true }));
    try {
      return await asyncFn();
    } finally {
      setLoadingStates((prev) => ({ ...prev, [stateKey]: false }));
    }
  };

  // Mutation functions using the helper
  const createRoleMutation = (roleData) => withLoadingState('creating', () => createRole(roleData));

  const updateRoleMutation = (id, data) => withLoadingState('updating', () => updateRole(id, data));

  const deleteRoleMutation = (roleId) => withLoadingState('deleting', () => deleteRole(roleId));

  const upsertRoleMutation = (roleData) =>
    withLoadingState('upserting', () => upsertRole(roleData));

  const assignPermissionToRoleMutation = (roleId, permissionId) =>
    withLoadingState('assigningPermission', () => assignPermissionToRole(roleId, permissionId));

  const removePermissionFromRoleMutation = (roleId, permissionId) =>
    withLoadingState('removingPermission', () => removePermissionFromRole(roleId, permissionId));

  // Calculate if any mutation is in progress
  const isMutating = Object.values(loadingStates).some(Boolean);

  return {
    createRole: createRoleMutation,
    updateRole: updateRoleMutation,
    deleteRole: deleteRoleMutation,
    upsertRole: upsertRoleMutation,
    assignPermissionToRole: assignPermissionToRoleMutation,
    removePermissionFromRole: removePermissionFromRoleMutation,
    isCreating: loadingStates.creating,
    isUpdating: loadingStates.updating,
    isDeleting: loadingStates.deleting,
    isUpserting: loadingStates.upserting,
    isAssigningPermission: loadingStates.assigningPermission,
    isRemovingPermission: loadingStates.removingPermission,
    isMutating,
  };
}
