'use client';

import { useState } from 'react';

import { createOrder, updateOrder, createOrderItem, createOrderHistory } from './order-service';

/**
 * Hook để thực hiện các thao tác mutation phức tạp với đơn hàng
 * @returns {Object} - <PERSON><PERSON><PERSON> hàm mutation
 */
export function useOrderMutations() {
  const [isMutating, setIsMutating] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Tạo đơn hàng mới với các sản phẩm
   * @param {Object} orderData - D<PERSON> liệu đơn hàng
   * @returns {Promise<Object>} - Kết quả từ API
   */
  const createCompleteOrder = async (orderData) => {
    setIsMutating(true);
    setError(null);
    try {
      // Tách dữ liệu đơn hàng và sản phẩm
      const { orderItems, ...orderInfo } = orderData;

      // 1. Tạo đơn hàng cơ bản trước
      const orderResult = await createOrder({
        ...orderInfo,
      });

      if (!orderResult.success) {
        return orderResult;
      }

      const orderId = orderResult.data[0].id;

      // 2. Thêm các sản phẩm vào đơn hàng
      if (orderItems && orderItems.length > 0) {
        const itemPromises = orderItems.map((item) =>
          createOrderItem({
            ...item,
            orderId,
            variantId: item.variantId || null,
          })
        );

        await Promise.all(itemPromises);
      }

      // 3. Tạo lịch sử trạng thái đơn hàng
      await createOrderHistory({
        orderId,
        status: orderInfo.status,
        comment: 'Đơn hàng được tạo',
      });

      return {
        success: true,
        data: orderResult.data,
        error: null,
      };
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsMutating(false);
    }
  };

  /**
   * Cập nhật trạng thái đơn hàng
   * @param {string} orderId - ID đơn hàng
   * @param {string} status - Trạng thái mới của đơn hàng
   * @param {string} previousStatus - Trạng thái trước đó của đơn hàng
   * @param {string} comment - Ghi chú khi cập nhật trạng thái
   * @param {string} userId - ID người dùng thực hiện cập nhật
   * @returns {Promise<Object>} - Kết quả từ API
   */
  const updateOrderStatus = async (
    orderId,
    status,
    previousStatus,
    comment = '',
    userId = null
  ) => {
    setIsMutating(true);
    setError(null);
    try {
      // 1. Cập nhật trạng thái đơn hàng
      const orderResult = await updateOrder(orderId, {
        status: status || null,
      });

      if (!orderResult.success) {
        return orderResult;
      }
      const orderHistoryItem = {
        orderId,
        status: status || null,
        previousStatus: previousStatus || null,
        userId: userId || null,
        comment,
      };

      // 2. Tạo lịch sử trạng thái đơn hàng
      await createOrderHistory(orderHistoryItem);

      return {
        success: true,
        data: orderResult.data,
        error: null,
      };
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsMutating(false);
    }
  };

  return {
    createCompleteOrder,
    updateOrderStatus,
    isMutating,
    error,
  };
}
