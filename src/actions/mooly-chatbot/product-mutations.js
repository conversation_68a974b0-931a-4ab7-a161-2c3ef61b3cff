'use client';

import { useState } from 'react';

import { supabase } from 'src/lib/supabase';

import { useAuthContext } from 'src/auth/hooks';

import storageService from './storage-service';
import weaviateService from './weaviate-service';
import { createProductBase, updateProductBase, deleteProductBase } from './product-api';
import {
  updateProductVariant,
  createProductVariant,
  deleteAllProductVariants,
} from './product-variant-service';

/**
 * Hook để thực hiện các thao tác mutation với sản phẩm
 * @returns {Object} - Các hàm mutation
 */

function compareImages(defaultImages, currentImages) {
  // Xác định các hình cần xóa (có trong defaultImages nhưng không còn trong currentImages)
  const imagesToDelete = defaultImages.filter(
    (defaultImg) =>
      // Kiểm tra xem URL này có còn trong currentImages không
      !currentImages.some((currentImg) => {
        // Nếu currentImg là string (URL), so sánh trực tiếp
        if (typeof currentImg === 'string') {
          return currentImg === defaultImg;
        }
        // Nếu currentImg là object, nó là file mới, không phải URL cũ
        return false;
      })
  );

  // Xác định các hình cần upload (các object File trong currentImages)
  const imagesToUpload = currentImages.filter(
    (img) =>
      // Nếu là object, đó là file cần upload
      typeof img === 'object' && img !== null
  );

  // Xác định các hình giữ nguyên (có trong cả hai mảng)
  const unchangedImages = currentImages.filter(
    (img) =>
      // Nếu là string và có trong defaultImages
      typeof img === 'string' && defaultImages.includes(img)
  );

  return {
    toDelete: imagesToDelete,
    toUpload: imagesToUpload,
    unchanged: unchangedImages,
  };
}

export function useProductMutations() {
  const [isMutating, setIsMutating] = useState(false);
  const [error, setError] = useState(null);
  // Lấy thông tin tenant_id từ user hiện tại
  const { user } = useAuthContext();
  const tenantId = user?.app_metadata?.tenant_id || '';
  /**
   * Tạo sản phẩm mới với hình ảnh và biến thể
   * @param {Object} productData - Dữ liệu sản phẩm
   * @returns {Promise<Object>} - Kết quả từ API
   */
  const createProduct = async (productData) => {
    setIsMutating(true);
    setError(null);
    try {
      // Tách dữ liệu sản phẩm, hình ảnh và biến thể
      const { images, variants } = productData;

      // Upload multiple images simultaneously using Promise.all
      let uploadResults = [];
      if (images && images.length > 0) {
        // Create an array of upload promises
        const uploadPromises = images
          .filter((image) => typeof image === 'object' && image !== null)
          .map(async (image) => {
            // Generate a unique filename for each image
            const fileName = storageService.generateUniqueFileName(image.name);

            try {
              // Sử dụng storageService để tải lên hình ảnh
              const filePath = `products/${fileName}`;
              const uploadResult = await storageService.uploadFile('public', filePath, image, {
                upsert: true,
                cacheControl: '3600',
              });

              if (!uploadResult.success) {
                return { success: false, error: uploadResult.error, fileName };
              }

              // Return the upload result
              return {
                success: true,
                publicUrl: uploadResult.publicUrl,
                fileName,
                originalFile: image,
              };
            } catch (uploadError) {
              return { success: false, error: uploadError, fileName };
            }
          });

        // Execute all upload promises simultaneously
        uploadResults = await Promise.all(uploadPromises);

        // Process successful uploads
        const successfulUploads = uploadResults.filter((result) => result.success);

        // Replace file objects with URLs in the images array
        if (successfulUploads.length > 0) {
          productData.images = productData.images.map((img) => {
            // If this is a file that was just uploaded, replace it with its URL
            if (typeof img === 'object' && img !== null) {
              // Find the corresponding upload result
              const uploadResult = uploadResults.find(
                (result) => result.success && result.originalFile === img
              );

              // If found, return the URL, otherwise keep the original
              return uploadResult ? uploadResult.publicUrl : img;
            }

            // Keep strings (URLs) as they are
            return img;
          });
        }
      }

      // Xử lý avatar
      if (productData.avatar && typeof productData.avatar === 'object') {
        const avatarFile = productData.avatar;

        // Kiểm tra trong danh sách hình ảnh đã upload
        if (uploadResults && uploadResults.length > 0) {
          // Tìm kiếm trong kết quả upload xem có hình ảnh nào trùng với avatar không
          const matchingUpload = uploadResults.find((result) => {
            if (!result.success) return false;

            // So sánh dựa trên object reference (cùng một object)
            if (result.originalFile === avatarFile) {
              return true;
            }

            // So sánh dựa trên tên file và kích thước nếu có
            if (
              avatarFile.name &&
              result.originalFile.name === avatarFile.name &&
              avatarFile.size &&
              result.originalFile.size === avatarFile.size
            ) {
              return true;
            }

            // So sánh dựa trên path nếu có
            if (avatarFile.path && result.originalFile.path === avatarFile.path) {
              return true;
            }

            return false;
          });

          // Nếu tìm thấy, sử dụng URL từ kết quả upload
          if (matchingUpload) {
            productData.avatar = matchingUpload.publicUrl;
          }
        }
      }
      delete productData.media;
      delete productData.variants;
      delete productData.newVariants;
      // 1. Tạo sản phẩm cơ bản trước
      const productResult = await createProductBase({
        ...productData,
      });

      if (!productResult.success) {
        return productResult;
      }

      const productId = productResult.data[0].id;

      // Mảng chứa các promise để thực hiện song song
      const promises = [];

      // 4. Thêm biến thể sản phẩm nếu có
      if (variants && variants.length > 0) {
        // Xử lý tất cả các biến thể, không chỉ những biến thể có thuộc tính
        const variantPromises = variants.map(async (variant) => {
          if (!variant) return null;

          // Xử lý avatar của variant nếu có
          if (variant.avatar && typeof variant.avatar === 'object' && variant.avatar !== null) {
            // Kiểm tra xem avatar có trong danh sách hình ảnh đã upload không
            if (uploadResults && uploadResults.length > 0) {
              // Tìm kiếm trong kết quả upload xem có hình ảnh nào trùng với avatar không
              const matchingUpload = uploadResults.find((result) => {
                if (!result.success) return false;

                // So sánh dựa trên object reference (cùng một object)
                if (result.originalFile === variant.avatar) {
                  return true;
                }

                // So sánh dựa trên tên file và kích thước nếu có
                if (
                  variant.avatar.name &&
                  result.originalFile.name === variant.avatar.name &&
                  variant.avatar.size &&
                  result.originalFile.size === variant.avatar.size
                ) {
                  return true;
                }

                // So sánh dựa trên path nếu có
                if (variant.avatar.path && result.originalFile.path === variant.avatar.path) {
                  return true;
                }

                return false;
              });

              // Nếu tìm thấy, sử dụng URL từ kết quả upload
              if (matchingUpload) {
                variant.avatar = matchingUpload.publicUrl;
              }
            }
          }
          // Sử dụng hàm mới để tạo biến thể với thuộc tính JSON
          return createProductVariant({ ...variant, productId });
        });

        promises.push(...variantPromises.filter(Boolean));
      }

      // Thực hiện tất cả các promise
      await Promise.all(promises.filter(Boolean));

      // Đồng bộ sản phẩm với Weaviate nếu có hình ảnh
      if (productData.images && productData.images.length > 0) {
        try {
          // Không cần khai báo botId vì sẽ sử dụng mảng rỗng

          // Tạo thông tin chung cho sản phẩm
          const productInfo = {
            name: productData.name,
            description: productData.description || '',
            tenant_id: tenantId,
            bot_id: [], // Mặc định là mảng rỗng khi thêm mới
            product_id: productResult.data[0].id,
            is_active: productData.isActive,
            isNewProduct: true, // Đánh dấu đây là sản phẩm mới
          };

          // Tạo danh sách đối tượng sản phẩm từ danh sách hình ảnh
          const productObjects = weaviateService.createProductObjectsFromImages(
            productData.images,
            productInfo
          );

          // Gọi API để thêm các sản phẩm vào Weaviate
          if (productObjects.length > 0) {
            await weaviateService.addProducts(productObjects);
          }
        } catch (weaviateError) {
          console.error('Error syncing product with Weaviate:', weaviateError);
          // Không ảnh hưởng đến kết quả trả về nếu đồng bộ Weaviate thất bại
        }
      }

      // Trả về kết quả thành công với ID sản phẩm
      return {
        success: true,
        data: productResult.data,
        error: null,
      };
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsMutating(false);
    }
  };

  /**
   * Cập nhật sản phẩm
   * @param {string} productId - ID sản phẩm cần cập nhật
   * @param {Object} productData - Dữ liệu sản phẩm
   * @returns {Promise<Object>} - Kết quả từ API
   */
  const updateProduct = async ({ productId, productData, defaultValues }) => {
    if (!productId) {
      return { success: false, error: new Error('Thiếu ID sản phẩm'), data: null };
    }

    setIsMutating(true);
    setError(null);
    try {
      // Tách dữ liệu sản phẩm, hình ảnh và biến thể
      // const { images, variants, attributes, ...productFields } = productData;
      const imageChanges = compareImages(defaultValues.images, productData.images);

      // Upload multiple images simultaneously using Promise.all
      let uploadResults = [];
      if (imageChanges?.toUpload && imageChanges.toUpload.length > 0) {
        // Create an array of upload promises
        const uploadPromises = imageChanges.toUpload.map(async (image) => {
          // Generate a unique filename for each image
          const fileName = storageService.generateUniqueFileName(image.name);

          try {
            // Sử dụng storageService để tải lên hình ảnh
            const filePath = `products/${fileName}`;
            const uploadResult = await storageService.uploadFile('public', filePath, image, {
              upsert: true,
              cacheControl: '3600',
            });

            if (!uploadResult.success) {
              return { success: false, error: uploadResult.error, fileName };
            }

            // Return the upload result
            return {
              success: true,
              publicUrl: uploadResult.publicUrl,
              fileName,
              originalFile: image,
            };
          } catch (uploadError) {
            return { success: false, error: uploadError, fileName };
          }
        });

        // Execute all upload promises simultaneously
        uploadResults = await Promise.all(uploadPromises);

        // Process successful uploads
        const successfulUploads = uploadResults.filter((result) => result.success);

        // Add the uploaded images to the product data
        if (successfulUploads.length > 0) {
          // Replace file objects with URLs in the images array
          productData.images = productData.images.map((img) => {
            // If this is a file that was just uploaded, replace it with its URL
            if (typeof img === 'object' && img !== null) {
              // Find the corresponding upload result
              const uploadResult = uploadResults.find(
                (result) => result.success && result.originalFile === img
              );

              // If found, return the URL, otherwise keep the original
              return uploadResult ? uploadResult.publicUrl : img;
            }

            // Keep strings (URLs) as they are
            return img;
          });
        }
      }
      if (imageChanges?.toDelete && imageChanges.toDelete.length > 0) {
        storageService.deleteFilesWithPublicUrl('public', imageChanges.toDelete);
      }
      // Xử lý avatar
      if (productData.avatar && typeof productData.avatar === 'object') {
        const avatarFile = productData.avatar;

        // Kiểm tra trong danh sách hình ảnh đã upload
        if (uploadResults && uploadResults.length > 0) {
          // Tìm kiếm trong kết quả upload xem có hình ảnh nào trùng với avatar không
          const matchingUpload = uploadResults.find((result) => {
            if (!result.success) return false;

            // So sánh dựa trên object reference (cùng một object)
            if (result.originalFile === avatarFile) {
              return true;
            }

            // So sánh dựa trên tên file và kích thước nếu có
            if (
              avatarFile.name &&
              result.originalFile.name === avatarFile.name &&
              avatarFile.size &&
              result.originalFile.size === avatarFile.size
            ) {
              return true;
            }

            // So sánh dựa trên path nếu có
            if (avatarFile.path && result.originalFile.path === avatarFile.path) {
              return true;
            }

            return false;
          });

          // Nếu tìm thấy, sử dụng URL từ kết quả upload
          if (matchingUpload) {
            productData.avatar = matchingUpload.publicUrl;
          }
        }
      }

      if (productData.newVariants && productData.newVariants.length > 0) {
        productData.variants = productData.newVariants;

        // Xử lý cập nhật các variant

        // Tạo mảng các promise để xử lý song song
        const variantPromises = productData.variants.map(async (variant) => {
          if (!variant || !variant.id) return null;

          // Chuẩn bị dữ liệu cập nhật cho variant (chỉ avatar và giá, không cập nhật tồn kho)
          const variantUpdateData = {
            price: variant.price,
            sale_price: variant.salePrice,
            cost_price: variant.costPrice,
            sku: variant.sku,
            barcode: variant.barcode,
            is_active: variant.isActive,
            // Không cập nhật stock_quantity để giữ nguyên tồn kho
          };

          // Xử lý avatar nếu có
          if (variant.avatar) {
            // Nếu avatar là file object, cần upload
            if (
              typeof variant.avatar === 'object' &&
              variant.avatar !== null &&
              !variant.avatar.url
            ) {
              // Kiểm tra xem avatar có trong danh sách hình ảnh đã upload không
              if (uploadResults && uploadResults.length > 0) {
                // Tìm kiếm trong kết quả upload xem có hình ảnh nào trùng với avatar không
                const matchingUpload = uploadResults.find((result) => {
                  if (!result.success) return false;

                  // So sánh dựa trên object reference (cùng một object)
                  if (result.originalFile === variant.avatar) {
                    return true;
                  }

                  // So sánh dựa trên tên file và kích thước nếu có
                  if (
                    variant.avatar.name &&
                    result.originalFile.name === variant.avatar.name &&
                    variant.avatar.size &&
                    result.originalFile.size === variant.avatar.size
                  ) {
                    return true;
                  }

                  // So sánh dựa trên path nếu có
                  if (variant.avatar.path && result.originalFile.path === variant.avatar.path) {
                    return true;
                  }

                  return false;
                });

                // Nếu tìm thấy, sử dụng URL từ kết quả upload
                if (matchingUpload) {
                  variant.avatar = matchingUpload.publicUrl;
                }
              }
            }

            // Lưu avatar vào dữ liệu cập nhật
            if (typeof variant.avatar === 'string') {
              // Nếu avatar là URL, lưu vào trường avatar
              variantUpdateData.avatar = variant.avatar;
            }
          }

          // Cập nhật variant trong database
          return updateProductVariant(variant.id, variantUpdateData);
        });

        // Thực hiện tất cả các promise cập nhật variant
        await Promise.all(variantPromises.filter(Boolean));
      }

      delete productData.media;
      delete productData.variants;
      delete productData.newVariants;

      try {
        // 1. Cập nhật thông tin cơ bản của sản phẩm
        const productResult = await updateProductBase(productId, {
          ...productData,
        });

        if (!productResult.success) {
          return productResult;
        }

        // Đồng bộ sản phẩm với Weaviate dựa trên danh sách hình ảnh
        try {
          // Không cần khai báo botId vì không sử dụng trong trường hợp cập nhật

          // Tạo thông tin chung cho sản phẩm
          const productInfo = {
            name: productData.name,
            description: productData.description || '',
            tenant_id: tenantId,
            product_id: productId,
            is_active: productData.isActive,
            isNewProduct: false, // Đánh dấu đây không phải là sản phẩm mới
          };

          // Đồng bộ danh sách hình ảnh với Weaviate
          // So sánh danh sách hình ảnh hiện tại với danh sách hình ảnh trước đó
          await weaviateService.syncProductImages(
            productData.images,
            defaultValues.images,
            productInfo
          );
        } catch (weaviateError) {
          console.error('Error syncing product with Weaviate:', weaviateError);
          // Không ảnh hưởng đến kết quả trả về nếu đồng bộ Weaviate thất bại
        }

        return {
          success: true,
          data: productResult.data,
          error: null,
        };
      } catch (updateError) {
        // Kiểm tra nếu lỗi liên quan đến ràng buộc khóa ngoại
        if (
          updateError.message &&
          updateError.message.includes('violates foreign key constraint')
        ) {
          // Thử cập nhật với cách khác để tránh vi phạm ràng buộc khóa ngoại
          // Sử dụng truy vấn SQL trực tiếp để cập nhật sản phẩm
          const { data, error: updateDbError } = await supabase
            .from('products')
            .update({
              name: productData.name,
              slug: productData.slug,
              description: productData.description,
              short_description: productData.shortDescription,
              type: productData.type,
              sku: productData.sku,
              barcode: productData.barcode,
              price: productData.price,
              sale_price: productData.salePrice,
              cost_price: productData.costPrice,
              weight: productData.weight,
              length: productData.length,
              width: productData.width,
              height: productData.height,
              attributes: productData.attributes,
              is_active: productData.isActive,
              is_featured: productData.isFeatured,
              seo_title: productData.seoTitle,
              seo_description: productData.seoDescription,
              meta_keywords: productData.metaKeywords,
              avatar: productData.avatar,
              images: productData.images,
              updated_at: new Date(),
              // Không cập nhật các trường có thể gây ra vi phạm ràng buộc khóa ngoại
            })
            .eq('id', productId)
            .select();

          if (updateDbError) {
            throw updateDbError;
          }

          return {
            success: true,
            data,
            error: null,
          };
        } else {
          // Nếu không phải lỗi ràng buộc khóa ngoại, ném lại lỗi
          throw updateError;
        }
      }
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsMutating(false);
    }
  };

  /**
   * Xóa sản phẩm
   * @param {string} productId - ID sản phẩm cần xóa
   * @returns {Promise<Object>} - Kết quả từ API
   */
  const deleteProduct = async (productId) => {
    if (!productId) {
      return { success: false, error: new Error('Thiếu ID sản phẩm'), data: null };
    }

    setIsMutating(true);
    setError(null);
    try {
      // Lấy thông tin sản phẩm và images
      let productImages = [];
      try {
        const { data } = await supabase
          .from('products')
          .select('images')
          .eq('id', productId)
          .single();

        productImages = data?.images || [];
      } catch (fetchError) {
        console.error('Error fetching product images:', fetchError);
      }

      // Lấy danh sách biến thể của sản phẩm
      const { data: variants } = await supabase
        .from('product_variants')
        .select('id')
        .eq('product_id', productId);

      // Xóa tất cả thuộc tính của biến thể và biến thể
      await deleteAllProductVariants(productId);

      // Xóa tất cả hình ảnh sản phẩm từ storage
      if (productImages && productImages.length > 0) {
        await storageService.deleteFilesWithPublicUrl('public', productImages);
      }

      // Đồng bộ xóa sản phẩm với Weaviate
      try {
        // Xóa tất cả sản phẩm trong Weaviate dựa trên product_id
        await weaviateService.deleteProductById(productId, tenantId);
      } catch (weaviateError) {
        console.error('Error deleting products from Weaviate:', weaviateError);
        // Không ảnh hưởng đến kết quả trả về nếu đồng bộ Weaviate thất bại
      }

      // Xóa sản phẩm
      const result = await deleteProductBase(productId);

      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsMutating(false);
    }
  };

  return {
    createProduct,
    updateProduct,
    deleteProduct,
    isMutating,
    error,
  };
}
