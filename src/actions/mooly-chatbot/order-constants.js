'use client';

/**
 * <PERSON><PERSON><PERSON> hằng số và cấu hình cho module đơn hàng
 */

// Tên bảng trong Supabase
export const TABLE_NAME = 'orders';
export const ORDER_ITEMS_TABLE = 'order_items';
export const ORDER_STATUSES_TABLE = 'order_statuses';
export const ORDER_HISTORY_TABLE = 'order_history';
export const CUSTOMERS_TABLE = 'customers';
export const CUSTOMER_ADDRESSES_TABLE = 'customer_addresses';

// Cấu hình mặc định
export const DEFAULT_ORDER_OPTIONS = {
  orderBy: 'createdAt',
  ascending: false,
};

// Trạng thái đơn hàng
export const ORDER_STATUS = {
  // Trạng thái chung
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  PROCESSING: 'processing',
  PAID: 'paid',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded',

  // Trạng thái cho sản phẩm vật lý
  PACKAGING: 'packaging',
  SHIPPING: 'shipping',
  DELIVERED: 'delivered',

  // Trạng thái cho sản phẩm số
  PREPARING: 'preparing',
  READY_DOWNLOAD: 'ready_download',
  SENT: 'sent',

  // Trạng thái cho dịch vụ
  SCHEDULING: 'scheduling',
  IN_PROGRESS: 'in_progress',
  PROVIDED: 'provided',
};

// Phương thức thanh toán
export const PAYMENT_METHODS = {
  COD: 'cod',
  BANK_TRANSFER: 'bank_transfer',
  CREDIT_CARD: 'credit_card',
  MOMO: 'momo',
  ZALOPAY: 'zalopay',
};

// Phương thức vận chuyển
export const SHIPPING_METHODS = {
  STANDARD: 'standard',
  EXPRESS: 'express',
  FREE: 'free',
};

// Tên trường trong database - đồng bộ chính xác với schema DB
export const DB_FIELDS = {
  ID: 'id',
  STORE_ID: 'storeId',
  CUSTOMER_ID: 'customerId',
  STATUS: 'status',
  ORDER_NUMBER: 'orderNumber',
  TOTAL_AMOUNT: 'totalAmount',
  SUBTOTAL: 'subtotal',
  SHIPPING_AMOUNT: 'shippingAmount',
  TAX_AMOUNT: 'taxAmount',
  DISCOUNT_AMOUNT: 'discountAmount',
  SHIPPING_ADDRESS_ID: 'shippingAddressId',
  BILLING_ADDRESS_ID: 'billingAddressId',
  SHIPPING_METHOD: 'shippingMethod',
  PAYMENT_METHOD: 'paymentMethod',
  NOTES: 'notes',
  CUSTOMER_EMAIL: 'customerEmail',
  CUSTOMER_PHONE: 'customerPhone',
  CUSTOMER_NAME: 'customerName',
  CREATED_AT: 'createdAt',
  UPDATED_AT: 'updatedAt',
};

// Tên trường trong UI form
export const FORM_FIELDS = {
  ORDER_NUMBER: 'orderNumber',
  CUSTOMER_ID: 'customerId',
  STATUS: 'status',
  TOTAL_AMOUNT: 'totalAmount',
  SUBTOTAL: 'subtotal',
  SHIPPING_AMOUNT: 'shippingAmount',
  TAX_AMOUNT: 'taxAmount',
  DISCOUNT_AMOUNT: 'discountAmount',
  SHIPPING_ADDRESS_ID: 'shippingAddressId',
  BILLING_ADDRESS_ID: 'billingAddressId',
  SHIPPING_METHOD: 'shippingMethod',
  PAYMENT_METHOD: 'paymentMethod',
  NOTES: 'notes',
  CUSTOMER_EMAIL: 'customerEmail',
  CUSTOMER_PHONE: 'customerPhone',
  CUSTOMER_NAME: 'customerName',
  ORDER_ITEMS: 'orderItems',
};

// Mapping giữa trường form và trường database
export const FORM_TO_DB_MAPPING = {
  [FORM_FIELDS.ORDER_NUMBER]: DB_FIELDS.ORDER_NUMBER,
  [FORM_FIELDS.CUSTOMER_ID]: DB_FIELDS.CUSTOMER_ID,
  [FORM_FIELDS.STATUS]: DB_FIELDS.STATUS,
  [FORM_FIELDS.TOTAL_AMOUNT]: DB_FIELDS.TOTAL_AMOUNT,
  [FORM_FIELDS.SUBTOTAL]: DB_FIELDS.SUBTOTAL,
  [FORM_FIELDS.SHIPPING_AMOUNT]: DB_FIELDS.SHIPPING_AMOUNT,
  [FORM_FIELDS.TAX_AMOUNT]: DB_FIELDS.TAX_AMOUNT,
  [FORM_FIELDS.DISCOUNT_AMOUNT]: DB_FIELDS.DISCOUNT_AMOUNT,
  [FORM_FIELDS.SHIPPING_ADDRESS_ID]: DB_FIELDS.SHIPPING_ADDRESS_ID,
  [FORM_FIELDS.BILLING_ADDRESS_ID]: DB_FIELDS.BILLING_ADDRESS_ID,
  [FORM_FIELDS.SHIPPING_METHOD]: DB_FIELDS.SHIPPING_METHOD,
  [FORM_FIELDS.PAYMENT_METHOD]: DB_FIELDS.PAYMENT_METHOD,
  [FORM_FIELDS.NOTES]: DB_FIELDS.NOTES,
  [FORM_FIELDS.CUSTOMER_EMAIL]: DB_FIELDS.CUSTOMER_EMAIL,
  [FORM_FIELDS.CUSTOMER_PHONE]: DB_FIELDS.CUSTOMER_PHONE,
  [FORM_FIELDS.CUSTOMER_NAME]: DB_FIELDS.CUSTOMER_NAME,
};

// Các tùy chọn trạng thái đơn hàng
export const ORDER_STATUS_OPTIONS = [
  // Trạng thái chung
  { value: 'pending', label: 'Chờ xác nhận', color: 'warning' },
  { value: 'confirmed', label: 'Đã xác nhận', color: 'info' },
  { value: 'processing', label: 'Đang xử lý', color: 'info' },
  { value: 'paid', label: 'Đã thanh toán', color: 'success' },
  { value: 'completed', label: 'Hoàn thành', color: 'success' },
  { value: 'cancelled', label: 'Đã hủy', color: 'error' },
  { value: 'refunded', label: 'Hoàn tiền', color: 'secondary' },

  // Trạng thái cho sản phẩm vật lý
  { value: 'packaging', label: 'Đang đóng gói', color: 'info' },
  { value: 'shipping', label: 'Đang vận chuyển', color: 'info' },
  { value: 'delivered', label: 'Đã giao hàng', color: 'success' },

  // Trạng thái cho sản phẩm số
  { value: 'preparing', label: 'Đang chuẩn bị', color: 'info' },
  { value: 'ready_download', label: 'Sẵn sàng tải xuống', color: 'success' },
  { value: 'sent', label: 'Đã gửi', color: 'success' },

  // Trạng thái cho dịch vụ
  { value: 'scheduling', label: 'Đang lên lịch', color: 'info' },
  { value: 'in_progress', label: 'Đang thực hiện', color: 'info' },
  { value: 'provided', label: 'Đã cung cấp', color: 'success' },
];

// Các tùy chọn phương thức thanh toán
export const PAYMENT_METHOD_OPTIONS = [
  { value: 'cod', label: 'Thanh toán khi nhận hàng (COD)' },
  { value: 'bank_transfer', label: 'Chuyển khoản ngân hàng' },
  { value: 'credit_card', label: 'Thẻ tín dụng/ghi nợ' },
  { value: 'momo', label: 'Ví MoMo' },
  { value: 'zalopay', label: 'ZaloPay' },
];

// Các tùy chọn phương thức vận chuyển
export const SHIPPING_METHOD_OPTIONS = [
  { value: 'standard', label: 'Vận chuyển tiêu chuẩn' },
  { value: 'express', label: 'Vận chuyển nhanh' },
  { value: 'free', label: 'Miễn phí vận chuyển' },
];
