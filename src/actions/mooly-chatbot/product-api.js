'use client';

import { TABLE_NAME } from './product-constants';
import { fetchData, createData, updateData, deleteData, upsertData } from './supabase-utils';

/**
 * L<PERSON><PERSON> danh sách sản phẩm
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getProducts(options = {}) {
  return fetchData(TABLE_NAME, options);
}

/**
 * Lấy thông tin chi tiết sản phẩm theo ID
 * @param {string} productId - ID sản phẩm
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getProductById(productId) {
  if (!productId) {
    return { success: false, error: new Error('Thiếu ID sản phẩm'), data: null };
  }

  return fetchData(TABLE_NAME, {
    filters: { id: productId },
    single: true,
  });
}

/**
 * <PERSON><PERSON><PERSON> sản phẩm mới (chỉ thông tin cơ bản)
 * @param {Object} productData - Dữ liệu sản phẩm
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createProductBase(productData) {
  try {
    // Đảm bảo có stock_quantity mặc định nếu không được cung cấp
    if (productData.stock_quantity === undefined) {
      productData.stock_quantity = 0;
    }

    const result = await createData(TABLE_NAME, productData);
    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Cập nhật thông tin cơ bản của sản phẩm
 * @param {string} productId - ID sản phẩm
 * @param {Object} productData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateProductBase(productId, productData) {
  if (!productId) {
    return { success: false, error: new Error('Thiếu ID sản phẩm'), data: null };
  }

  try {
    // Cập nhật thông tin sản phẩm
    const result = await updateData(TABLE_NAME, productData, { id: productId });
    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Xóa sản phẩm
 * @param {string} productId - ID sản phẩm
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteProductBase(productId) {
  if (!productId) {
    return { success: false, error: new Error('Thiếu ID sản phẩm'), data: null };
  }

  return deleteData(TABLE_NAME, { id: productId });
}

/**
 * Tạo hoặc cập nhật sản phẩm
 * @param {Object} productData - Dữ liệu sản phẩm
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertProductBase(productData) {
  try {
    const result = await upsertData(TABLE_NAME, productData);
    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}
