'use client';

/**
 * <PERSON><PERSON><PERSON> tổng hợp các dịch vụ liên quan đến sản phẩm
 * Cung cấp các API, hooks, và tiện ích để quản lý sản phẩm
 */

// Re-export từ các module con
export * from './product-api';
export * from './product-hooks';
export * from './product-mutations';
export * from './product-constants';
export * from './product-variant-service';
export * from './product-attribute-service';

// Import các hàm cần thiết để export riêng
import { getProducts } from './product-api';
import { useProduct, useProducts } from './product-hooks';
import { useProductMutations } from './product-mutations';

// Export các hàm chính để sử dụng trực tiếp
export { useProduct, useProducts, getProducts, useProductMutations };

// Import các service khác để sử dụng khi cần
import storageServiceImport from './storage-service';
import weaviateServiceImport from './weaviate-service';

// Export các service khác với tên khác để tránh xung đột
export {
  storageServiceImport as productStorageService,
  weaviateServiceImport as productWeaviateService
};
