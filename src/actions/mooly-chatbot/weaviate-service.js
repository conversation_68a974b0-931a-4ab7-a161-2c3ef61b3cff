'use client';

import {
  addProducts as serverAddProducts,
  updateProduct as serverUpdateProduct,
  deleteProduct as serverDeleteProduct,
  deleteProductById as serverDeleteProductById,
  updateProductById as serverUpdateProductById,
  toggleProductActive as serverToggleProductActive,
} from 'src/app/actions/weaviate/products';

/**
 * Service để đồng bộ dữ liệu sản phẩm với Weaviate
 */
export const weaviateService = {
  /**
   * Tạo danh sách đối tượng sản phẩm từ danh sách hình ảnh
   * @param {Array<string>} images - Danh sách URL hình ảnh
   * @param {Object} productInfo - Thông tin chung của sản phẩm
   * @param {string} productInfo.name - Tên sản phẩm
   * @param {string} productInfo.description - <PERSON><PERSON> tả sản phẩm
   * @param {string} productInfo.tenant_id - ID của tenant
   * @param {string} productInfo.bot_id - ID của bot hoặc mảng các bot_id
   * @param {string} productInfo.product_id - ID của sản phẩm
   * @returns {Array<Object>} - Danh sách đối tượng sản phẩm
   */
  createProductObjectsFromImages(images, productInfo) {
    if (!images || !Array.isArray(images) || images.length === 0) {
      return [];
    }

    return images
      .filter((image) => typeof image === 'string' && image.trim() !== '')
      .map((image_url) => {
        // Tạo đối tượng cơ bản
        const productObject = {
          name: productInfo.name,
          description: productInfo.description || '',
          image_url,
          tenant_id: productInfo.tenant_id,
          product_id: productInfo.product_id,
          is_active: productInfo.is_active !== undefined ? productInfo.is_active : true,
        };

        // Thêm bot_id nếu có giá trị hoặc là mảng rỗng
        if (productInfo.bot_id !== undefined && productInfo.bot_id !== null) {
          // Nếu là thêm mới và không có bot_id cụ thể, sử dụng mảng rỗng
          if (
            productInfo.isNewProduct &&
            (productInfo.bot_id === '' ||
              (Array.isArray(productInfo.bot_id) && productInfo.bot_id.length === 0))
          ) {
            productObject.bot_id = [];
          }
          // Nếu có bot_id cụ thể, sử dụng giá trị đó
          else if (productInfo.bot_id !== '') {
            productObject.bot_id = productInfo.bot_id;
          }
        }

        return productObject;
      });
  },

  /**
   * Thêm mới sản phẩm vào Weaviate
   * @param {Object} productData - Dữ liệu sản phẩm
   * @param {string} productData.name - Tên sản phẩm
   * @param {string} productData.description - Mô tả sản phẩm
   * @param {string} productData.image_url - URL hình ảnh sản phẩm
   * @param {string} productData.tenant_id - ID của tenant
   * @param {string|Array<string>} productData.bot_id - ID của bot hoặc mảng các bot_id
   * @param {string} productData.product_id - ID của sản phẩm
   * @returns {Promise<Object>} - Kết quả từ API
   */
  async addProduct(productData) {
    try {
      return await serverAddProducts([productData]);
    } catch (error) {
      console.error('Error adding product to Weaviate:', error);
      return {
        success: false,
        data: null,
        error: error.message,
      };
    }
  },

  /**
   * Thêm nhiều sản phẩm vào Weaviate
   * @param {Array<Object>} products - Danh sách sản phẩm
   * @returns {Promise<Object>} - Kết quả từ API
   */
  async addProducts(products) {
    if (!products || !Array.isArray(products) || products.length === 0) {
      return {
        success: true,
        data: { message: 'No products to add' },
        error: null,
      };
    }

    try {
      return await serverAddProducts(products);
    } catch (error) {
      console.error('Error adding products to Weaviate:', error);
      return {
        success: false,
        data: null,
        error: error.message,
      };
    }
  },

  /**
   * Cập nhật sản phẩm trong Weaviate
   * @param {Object} productData - Dữ liệu sản phẩm cần cập nhật
   * @returns {Promise<Object>} - Kết quả từ API
   */
  async updateProduct(productData) {
    try {
      return await serverUpdateProduct(productData);
    } catch (error) {
      console.error('Error updating product in Weaviate:', error);
      return {
        success: false,
        data: null,
        error: error.message,
      };
    }
  },

  /**
   * Xóa sản phẩm khỏi Weaviate dựa trên URL hình ảnh
   * @param {Object} deleteData - Dữ liệu để xóa sản phẩm
   * @param {string} deleteData.image_url - URL hình ảnh sản phẩm
   * @param {string} deleteData.tenant_id - ID của tenant
   * @returns {Promise<Object>} - Kết quả từ API
   */
  async deleteProduct(deleteData) {
    try {
      return await serverDeleteProduct(deleteData);
    } catch (error) {
      console.error('Error deleting product from Weaviate:', error);
      return {
        success: false,
        data: null,
        error: error.message,
      };
    }
  },

  /**
   * Xóa nhiều sản phẩm khỏi Weaviate dựa trên danh sách URL hình ảnh
   * @param {Array<string>} imageUrls - Danh sách URL hình ảnh
   * @param {string} tenantId - ID của tenant
   * @returns {Promise<Object>} - Kết quả từ API
   */
  async deleteProductsByImageUrls(imageUrls, tenantId) {
    if (!imageUrls || !Array.isArray(imageUrls) || imageUrls.length === 0) {
      return {
        success: true,
        data: { message: 'No images to delete' },
        error: null,
      };
    }

    try {
      const deletePromises = imageUrls
        .filter((imageUrl) => typeof imageUrl === 'string' && imageUrl.trim() !== '')
        .map((imageUrl) =>
          this.deleteProduct({
            image_url: imageUrl,
            tenant_id: tenantId,
          })
        );

      if (deletePromises.length === 0) {
        return {
          success: true,
          data: { message: 'No valid images to delete' },
          error: null,
        };
      }

      const results = await Promise.all(deletePromises);
      const failedDeletes = results.filter((result) => !result.success);

      return {
        success: failedDeletes.length === 0,
        data: results,
        error: failedDeletes.length > 0 ? 'Some products failed to delete' : null,
      };
    } catch (error) {
      console.error('Error deleting products from Weaviate:', error);
      return {
        success: false,
        data: null,
        error: error.message,
      };
    }
  },

  /**
   * Xóa tất cả sản phẩm khỏi Weaviate dựa trên product_id
   * @param {string} productId - ID của sản phẩm
   * @param {string} tenantId - ID của tenant
   * @returns {Promise<Object>} - Kết quả từ API
   */
  async deleteProductById(productId, tenantId) {
    try {
      return await serverDeleteProductById(productId, tenantId);
    } catch (error) {
      console.error('Error deleting product by ID from Weaviate:', error);
      return {
        success: false,
        data: null,
        error: error.message,
      };
    }
  },

  /**
   * Đồng bộ danh sách hình ảnh sản phẩm với Weaviate
   * @param {Array<string>} currentImages - Danh sách hình ảnh hiện tại
   * @param {Array<string>} previousImages - Danh sách hình ảnh trước đó
   * @param {Object} productInfo - Thông tin sản phẩm
   * @returns {Promise<Object>} - Kết quả đồng bộ
   */
  async syncProductImages(currentImages, previousImages, productInfo) {
    try {
      // Lọc ra các hình ảnh hợp lệ (string)
      const validCurrentImages = Array.isArray(currentImages)
        ? currentImages.filter((img) => typeof img === 'string' && img.trim() !== '')
        : [];

      const validPreviousImages = Array.isArray(previousImages)
        ? previousImages.filter((img) => typeof img === 'string' && img.trim() !== '')
        : [];

      // Xác định hình ảnh mới thêm vào
      const newImages = validCurrentImages.filter((img) => !validPreviousImages.includes(img));

      // Xác định hình ảnh đã bị xóa
      const deletedImages = validPreviousImages.filter((img) => !validCurrentImages.includes(img));

      // Tạo danh sách đối tượng sản phẩm từ hình ảnh mới
      const newProductObjects = this.createProductObjectsFromImages(newImages, productInfo);

      // Thêm các sản phẩm mới vào Weaviate
      let addResult = { success: true };
      if (newProductObjects.length > 0) {
        addResult = await this.addProducts(newProductObjects);
      }

      // Xóa các sản phẩm đã bị xóa khỏi Weaviate
      let deleteResult = { success: true };
      if (deletedImages.length > 0) {
        deleteResult = await this.deleteProductsByImageUrls(deletedImages, productInfo.tenant_id);
      }

      return {
        success: addResult.success && deleteResult.success,
        data: {
          added: newImages.length,
          deleted: deletedImages.length,
          addResult: addResult.data,
          deleteResult: deleteResult.data,
        },
        error: !addResult.success
          ? addResult.error
          : !deleteResult.success
            ? deleteResult.error
            : null,
      };
    } catch (error) {
      console.error('Error syncing product images with Weaviate:', error);
      return {
        success: false,
        data: null,
        error: error.message,
      };
    }
  },

  /**
   * Cập nhật thông tin sản phẩm trong Weaviate theo ID
   * @param {Object} updateData - Dữ liệu để cập nhật sản phẩm
   * @param {string} updateData.tenant_id - ID của tenant
   * @param {string} updateData.product_id - ID của sản phẩm
   * @param {boolean} [updateData.is_active] - Trạng thái kích hoạt mới (tùy chọn)
   * @param {Array<string>} [updateData.bot_id] - Danh sách ID của bot (tùy chọn)
   * @returns {Promise<Object>} - Kết quả từ API
   */
  async updateProductById(updateData) {
    try {
      return await serverUpdateProductById(updateData);
    } catch (error) {
      console.error('Error updating product in Weaviate:', error);
      return {
        success: false,
        data: null,
        error: error.message,
      };
    }
  },

  /**
   * Cập nhật trạng thái kích hoạt của sản phẩm trong Weaviate
   * @param {Object} toggleData - Dữ liệu để cập nhật trạng thái
   * @param {string} toggleData.tenant_id - ID của tenant
   * @param {boolean} toggleData.is_active - Trạng thái kích hoạt mới
   * @param {string} toggleData.product_id - ID của sản phẩm
   * @returns {Promise<Object>} - Kết quả từ API
   * @deprecated Sử dụng updateProductById thay thế
   */
  async toggleProductActive(toggleData) {
    try {
      return await serverToggleProductActive(toggleData);
    } catch (error) {
      console.error('Error toggling product active status in Weaviate:', error);
      return {
        success: false,
        data: null,
        error: error.message,
      };
    }
  },
};

// Export default để tương thích với cả nhập khẩu mặc định và có tên
export default weaviateService;
