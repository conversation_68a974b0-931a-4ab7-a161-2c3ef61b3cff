'use client';

import { useMemo, useState, useEffect, useContext, createContext } from 'react';

import { supabase } from 'src/lib/supabase';

import { fetchData, createData, updateData, deleteData } from './supabase-utils';

// Tên bảng trong Supabase
const TABLE_NAME = 'tenants';

// Tạo context cho tenant
const TenantContext = createContext(null);

/**
 * Provider cho tenant context
 * @param {Object} props - Props của component
 * @returns {JSX.Element} - Provider component
 */
export function TenantProvider({ children, defaultTenantId = null }) {
  const [tenantId, setTenantId] = useState(defaultTenantId);
  const [tenant, setTenant] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // L<PERSON>y thông tin tenant khi tenantId thay đổi
  useEffect(() => {
    if (!tenantId) {
      setTenant(null);
      setLoading(false);
      return;
    }

    async function fetchTenant() {
      setLoading(true);
      try {
        const { data, err } = await supabase
          .from(TABLE_NAME)
          .select('*')
          .eq('id', tenantId)
          .single();

        if (err) throw err;
        setTenant(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching tenant:', err);
        setError(err.message);
        setTenant(null);
      } finally {
        setLoading(false);
      }
    }

    fetchTenant();
  }, [tenantId]);

  // Lấy tenant ID từ user profile khi component mount
  useEffect(() => {
    async function fetchUserTenant() {
      try {
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) return;

        const { data, err } = await supabase
          .from('users')
          .select('tenant_id')
          .eq('id', user.id)
          .single();

        if (err) throw err;
        if (data && data.tenant_id) {
          setTenantId(data.tenant_id);
        }
      } catch (err) {
        console.error('Error fetching user tenant:', err);
        setError(err.message);
      }
    }

    if (!tenantId) {
      fetchUserTenant();
    }
  }, [tenantId]);

  const value = useMemo(
    () => ({
      tenantId,
      setTenantId,
      tenant,
      loading,
      error,
    }),
    [tenantId, tenant, loading, error]
  );

  return <TenantContext.Provider value={value}>{children}</TenantContext.Provider>;
}

/**
 * Hook để sử dụng tenant context
 * @returns {Object} - Tenant context
 */
export function useTenant() {
  const context = useContext(TenantContext);
  if (!context) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
}

/**
 * Lấy danh sách tenants
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getTenants(options = {}) {
  return fetchData(TABLE_NAME, options);
}

/**
 * Lấy thông tin tenant theo ID
 * @param {string} tenantId - ID của tenant
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getTenantById(tenantId) {
  if (!tenantId) return { success: false, error: 'Tenant ID is required', data: null };

  return fetchData(TABLE_NAME, {
    filters: { id: tenantId },
    single: true,
  });
}

/**
 * Tạo tenant mới
 * @param {Object} tenantData - Dữ liệu tenant
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createTenant(tenantData) {
  return createData(TABLE_NAME, tenantData);
}

/**
 * Cập nhật tenant
 * @param {string} tenantId - ID của tenant
 * @param {Object} tenantData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateTenant(tenantId, tenantData) {
  if (!tenantId) return { success: false, error: 'Tenant ID is required', data: null };

  return updateData(TABLE_NAME, tenantData, { id: tenantId });
}

/**
 * Xóa tenant
 * @param {string} tenantId - ID của tenant
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteTenant(tenantId) {
  if (!tenantId) return { success: false, error: 'Tenant ID is required', data: null };

  return deleteData(TABLE_NAME, { id: tenantId });
}

/**
 * Lấy danh sách người dùng trong tenant
 * @param {string} tenantId - ID của tenant
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getTenantUsers(tenantId) {
  if (!tenantId) return { success: false, error: 'Tenant ID is required', data: null };

  try {
    const { data, error } = await supabase.rpc('get_tenant_users', { tenant_id_param: tenantId });

    if (error) throw error;

    return {
      success: true,
      data,
      error: null,
    };
  } catch (error) {
    console.error('Error getting tenant users:', error);
    return {
      success: false,
      error: error.message || 'Có lỗi xảy ra khi lấy danh sách người dùng trong tenant.',
      data: null,
    };
  }
}

/**
 * Thêm người dùng vào tenant
 * @param {string} tenantId - ID của tenant
 * @param {string} userId - ID của người dùng
 * @param {string} role - Vai trò của người dùng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function addUserToTenant(tenantId, userId, role = 'viewer') {
  if (!tenantId) return { success: false, error: 'Tenant ID is required', data: null };
  if (!userId) return { success: false, error: 'User ID is required', data: null };

  try {
    const { data, error } = await supabase.rpc('add_user_to_tenant', {
      tenant_id_param: tenantId,
      user_id_param: userId,
      role_param: role,
    });

    if (error) throw error;

    return {
      success: true,
      data,
      error: null,
    };
  } catch (error) {
    console.error('Error adding user to tenant:', error);
    return {
      success: false,
      error: error.message || 'Có lỗi xảy ra khi thêm người dùng vào tenant.',
      data: null,
    };
  }
}

/**
 * Xóa người dùng khỏi tenant
 * @param {string} tenantId - ID của tenant
 * @param {string} userId - ID của người dùng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function removeUserFromTenant(tenantId, userId) {
  if (!tenantId) return { success: false, error: 'Tenant ID is required', data: null };
  if (!userId) return { success: false, error: 'User ID is required', data: null };

  try {
    const { data, error } = await supabase.rpc('remove_user_from_tenant', {
      tenant_id_param: tenantId,
      user_id_param: userId,
    });

    if (error) throw error;

    return {
      success: true,
      data,
      error: null,
    };
  } catch (error) {
    console.error('Error removing user from tenant:', error);
    return {
      success: false,
      error: error.message || 'Có lỗi xảy ra khi xóa người dùng khỏi tenant.',
      data: null,
    };
  }
}

/**
 * Cập nhật vai trò của người dùng trong tenant
 * @param {string} tenantId - ID của tenant
 * @param {string} userId - ID của người dùng
 * @param {string} role - Vai trò mới
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateUserTenantRole(tenantId, userId, role) {
  if (!tenantId) return { success: false, error: 'Tenant ID is required', data: null };
  if (!userId) return { success: false, error: 'User ID is required', data: null };
  if (!role) return { success: false, error: 'Role is required', data: null };

  try {
    const { data, error } = await supabase.rpc('update_user_tenant_role', {
      tenant_id_param: tenantId,
      user_id_param: userId,
      role_param: role,
    });

    if (error) throw error;

    return {
      success: true,
      data,
      error: null,
    };
  } catch (error) {
    console.error('Error updating user tenant role:', error);
    return {
      success: false,
      error: error.message || 'Có lỗi xảy ra khi cập nhật vai trò người dùng trong tenant.',
      data: null,
    };
  }
}
