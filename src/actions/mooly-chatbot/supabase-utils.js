'use client';

import { createClient } from 'src/utils/supabase/client';
import {
  camelToSnake,
  snakeToCamelObject,
  camelToSnakeObject,
} from 'src/utils/format-data/case-converter';

import {
  getCachedTenantId,
  addTenantIdToData,
  shouldAddTenantId,
  addTenantIdToFilters,
} from './tenant-middleware';

/**
 * Lấy dữ liệu từ Supabase với các tùy chọn lọc
 * @param {string} table - Tên bảng
 * @param {Object} options - Các tùy chọn truy vấn
 * @param {Object} options.filters - Các điều kiện lọc
 * @param {Array} options.columns - C<PERSON><PERSON> cột cần lấy
 * @param {string} options.orderBy - Cột để sắp xếp
 * @param {boolean} options.ascending - Sắp xếp tăng dần hay giảm dần
 * @param {number} options.limit - <PERSON><PERSON><PERSON><PERSON> hạn số lượng bản ghi
 * @param {number} options.offset - V<PERSON> trí bắt đầu
 * @param {boolean} options.single - Chỉ lấy một bản ghi
 * @param {boolean} options.count - Đếm số lượng bản ghi
 * @returns {Promise<Object>} - Kết quả từ Supabase
 */
export async function fetchData(table, options = {}) {
  try {
    const supabase = createClient();

    const {
      filters = {},
      columns = '*',
      orderBy,
      ascending = true,
      limit,
      offset,
      single = false,
      count,
    } = options;

    // Tự động thêm tenant_id vào filters nếu cần
    let updatedFilters = { ...filters };
    if (shouldAddTenantId(table)) {
      const tenantId = await getCachedTenantId();
      if (tenantId) {
        updatedFilters = addTenantIdToFilters(updatedFilters, tenantId);
      }
    }

    // Start query
    let query = supabase.from(table).select(columns, { count: count ? 'exact' : undefined });

    // Convert filters from camelCase to snake_case
    const snakeCaseFilters = camelToSnakeObject(updatedFilters);

    // Apply filters
    Object.entries(snakeCaseFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object' && value.operator) {
          // Handle complex operators
          const { operator, value: filterValue } = value;
          switch (operator) {
            case 'eq':
              query = query.eq(key, filterValue);
              break;
            case 'neq':
              query = query.neq(key, filterValue);
              break;
            case 'gt':
              query = query.gt(key, filterValue);
              break;
            case 'gte':
              query = query.gte(key, filterValue);
              break;
            case 'lt':
              query = query.lt(key, filterValue);
              break;
            case 'lte':
              query = query.lte(key, filterValue);
              break;
            case 'like':
              query = query.like(key, `%${filterValue}%`);
              break;
            case 'ilike':
              query = query.ilike(key, `%${filterValue}%`);
              break;
            case 'in':
              query = query.in(key, filterValue);
              break;
            case 'is':
              query = query.is(key, filterValue);
              break;
            default:
              query = query.eq(key, filterValue);
          }
        } else {
          // Default to equals operator
          query = query.eq(key, value);
        }
      }
    });

    // Sort (convert column name from camelCase to snake_case)
    if (orderBy) {
      const snakeCaseOrderBy = camelToSnake(orderBy);
      query = query.order(snakeCaseOrderBy, { ascending });
    }

    // Pagination
    if (limit) {
      query = query.limit(limit);
    }

    if (offset) {
      query = query.offset(offset);
    }

    // Execute query with improved timeout protection
    let result;
    try {
      // Add timeout protection to prevent hanging requests
      // Use longer timeout for background tabs
      const isVisible = document.visibilityState === 'visible';
      const timeoutDuration = isVisible ? 30000 : 120000; // 30s foreground, 120s background

      // Create an abortable fetch
      const abortController = new AbortController();

      // Register with global abort controller registry if available
      let unregisterController = () => {};
      if (typeof window !== 'undefined' && window.registerAbortController) {
        unregisterController = window.registerAbortController(abortController);
      }

      // We don't need the visibility change handler anymore since we use the global registry

      // Set timeout for the request
      const timeoutId = setTimeout(() => {
        abortController.abort('timeout');
      }, timeoutDuration);

      // Set abort signal on query
      query = query.abortSignal(abortController.signal);

      try {
        // Execute query
        result = single ? await query.single() : await query;
        clearTimeout(timeoutId);
      } catch (error) {
        clearTimeout(timeoutId);
        throw error;
      } finally {
        // Unregister from global registry
        unregisterController();
      }
    } catch (queryError) {
      // Check if this is an abort error
      if (
        queryError.name === 'AbortError' ||
        queryError.message?.includes('timeout') ||
        queryError.message?.includes('abort')
      ) {
        const reason = queryError.message?.includes('timeout')
          ? 'timeout'
          : queryError.message?.includes('visibility')
            ? 'visibility change'
            : 'abort';
        return {
          success: false,
          error: {
            message: `Query aborted - ${reason}`,
            code: 'QUERY_ABORTED',
            reason,
          },
          data: null,
          count: 0,
        };
      }

      return { success: false, error: queryError, data: null, count: 0 };
    }

    const { data, error, count: resultCount } = result;

    if (error) {
      console.error(`fetchData: Error in result for ${table}:`, error);
      return { success: false, error, data: null, count: 0 };
    }

    const transformedData = snakeToCamelObject(data);

    return {
      success: true,
      data: transformedData,
      error: null,
      count: count ? resultCount : undefined,
    };
  } catch (error) {
    return { success: false, error, data: null, count: 0 };
  }
}

/**
 * Tạo dữ liệu mới trong Supabase
 * @param {string} table - Tên bảng
 * @param {Object|Array} data - Dữ liệu cần tạo (đối tượng hoặc mảng các đối tượng)
 * @param {boolean} returnData - Có trả về dữ liệu sau khi tạo không
 * @returns {Promise<Object>} - Kết quả từ Supabase
 */
export async function createData(table, data, returnData = true) {
  try {
    const supabase = createClient();

    // Tự động thêm tenant_id vào data nếu cần
    let updatedData = data;
    if (shouldAddTenantId(table)) {
      const tenantId = await getCachedTenantId();
      if (tenantId) {
        updatedData = addTenantIdToData(data, tenantId);
      }
    }

    const snakeCaseData = camelToSnakeObject(updatedData);

    let query = supabase.from(table).insert(snakeCaseData);

    if (returnData) {
      query = query.select();
    }

    const { data: result, error } = await query;

    if (error) {
      return { success: false, error, data: null };
    }

    const transformedData = returnData ? snakeToCamelObject(result) : null;

    return { success: true, data: transformedData, error: null };
  } catch (error) {
    console.error(`Error in createData for ${table}:`, error);
    return { success: false, error, data: null };
  }
}

/**
 * Cập nhật dữ liệu trong Supabase
 * @param {string} table - Tên bảng
 * @param {Object} data - Dữ liệu cần cập nhật
 * @param {Object} filters - Các điều kiện lọc
 * @param {boolean} returnData - Có trả về dữ liệu sau khi cập nhật không
 * @returns {Promise<Object>} - Kết quả từ Supabase
 */
export async function updateData(table, data, filters, returnData = true) {
  try {
    const supabase = createClient();

    // Tự động thêm tenant_id vào filters nếu cần
    let updatedFilters = { ...filters };
    if (shouldAddTenantId(table)) {
      const tenantId = await getCachedTenantId();
      if (tenantId) {
        updatedFilters = addTenantIdToFilters(updatedFilters, tenantId);
      }
    }

    const snakeCaseData = camelToSnakeObject(data);
    const snakeCaseFilters = camelToSnakeObject(updatedFilters);

    let query = supabase.from(table).update(snakeCaseData);

    // Áp dụng các bộ lọc
    Object.entries(snakeCaseFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        query = query.eq(key, value);
      }
    });

    if (returnData) {
      query = query.select();
    }

    const { data: result, error } = await query;

    if (error) {
      return { success: false, error, data: null };
    }

    const transformedData = returnData ? snakeToCamelObject(result) : null;

    return { success: true, data: transformedData, error: null };
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Xóa dữ liệu trong Supabase
 * @param {string} table - Tên bảng
 * @param {Object} filters - Các điều kiện lọc
 * @param {boolean} returnData - Có trả về dữ liệu đã xóa không
 * @returns {Promise<Object>} - Kết quả từ Supabase
 */
export async function deleteData(table, filters, returnData = false) {
  try {
    const supabase = createClient();

    // Tự động thêm tenant_id vào filters nếu cần
    let updatedFilters = { ...filters };
    if (shouldAddTenantId(table)) {
      const tenantId = await getCachedTenantId();
      if (tenantId) {
        updatedFilters = addTenantIdToFilters(updatedFilters, tenantId);
      }
    }

    const snakeCaseFilters = camelToSnakeObject(updatedFilters);

    let query = supabase.from(table).delete();

    Object.entries(snakeCaseFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        query = query.eq(key, value);
      }
    });

    if (returnData) {
      query = query.select();
    }

    const { data: result, error } = await query;

    if (error) {
      return { success: false, error, data: null };
    }

    const transformedData = returnData ? snakeToCamelObject(result) : null;

    return { success: true, data: transformedData, error: null };
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Tạo hoặc cập nhật dữ liệu trong Supabase
 * @param {string} table - Tên bảng
 * @param {Object|Array} data - Dữ liệu cần tạo hoặc cập nhật
 * @param {Array} onConflict - Các cột xung đột
 * @param {boolean} returnData - Có trả về dữ liệu sau khi thực hiện không
 * @returns {Promise<Object>} - Kết quả từ Supabase
 */
export async function upsertData(table, data, onConflict = ['id'], returnData = true) {
  try {
    const supabase = createClient();

    // Tự động thêm tenant_id vào data nếu cần
    let updatedData = data;
    if (shouldAddTenantId(table)) {
      const tenantId = await getCachedTenantId();
      if (tenantId) {
        updatedData = addTenantIdToData(data, tenantId);
      }
    }

    const snakeCaseData = camelToSnakeObject(updatedData);

    let query = supabase.from(table).upsert(snakeCaseData, { onConflict, ignoreDuplicates: false });

    if (returnData) {
      query = query.select();
    }

    const { data: result, error } = await query;

    if (error) {
      return { success: false, error, data: null };
    }

    const transformedData = returnData ? snakeToCamelObject(result) : null;

    return { success: true, data: transformedData, error: null };
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Thực hiện RPC (Remote Procedure Call) trong Supabase
 * @param {string} functionName - Tên hàm RPC
 * @param {Object} params - Các tham số
 * @returns {Promise<Object>} - Kết quả từ Supabase
 */
export async function callRPC(functionName, params = {}) {
  try {
    const supabase = createClient();

    const snakeCaseParams = camelToSnakeObject(params);

    const sanitizedParams = {};
    Object.entries(snakeCaseParams).forEach(([key, value]) => {
      if (typeof value === 'boolean') {
        sanitizedParams[key] = value === true;
      } else if (value === null || value === undefined) {
        // Xử lý giá trị null/undefined
        sanitizedParams[key] = null;
      } else {
        sanitizedParams[key] = value;
      }
    });

    const { data, error } = await supabase.rpc(functionName, sanitizedParams);

    if (error) {
      return { success: false, error, data: null };
    }

    const transformedData = snakeToCamelObject(data);

    return { success: true, data: transformedData, error: null };
  } catch (error) {
    return { success: false, error, data: null };
  }
}
