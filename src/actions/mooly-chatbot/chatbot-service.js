'use client';

import { useMemo, useState, useEffect, useCallback } from 'react';

import storageService from './storage-service';
import { fetchData, createData, updateData, deleteData, upsertData } from './supabase-utils';

// Tên bảng trong Supabase
const TABLE_NAME = 'chatbot_configurations';

/**
 * L<PERSON>y danh sách cấu hình chatbot với các tùy chọn lọc
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getChatbots(options = {}) {
  return fetchData(TABLE_NAME, options);
}

/**
 * Lấy chi tiết một cấu hình chatbot theo ID
 * @param {string} chatbotId - ID của cấu hình chatbot
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getChatbotById(chatbotId) {
  if (!chatbotId) {
    console.log('getChatbotById: Chatbot ID is required but was not provided');
    return { success: false, error: 'Chatbot ID is required', data: null };
  }

  console.log('getChatbotById: Fetching chatbot with ID:', chatbotId);
  try {
    const result = await fetchData(TABLE_NAME, {
      filters: { id: chatbotId },
      single: true,
    });
    console.log('getChatbotById: Result:', result);
    return result;
  } catch (error) {
    console.error('getChatbotById: Error fetching chatbot:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Lấy cấu hình chatbot theo store ID
 * @param {string} storeId - ID của cửa hàng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getChatbotByStoreId(storeId) {
  if (!storeId) return { success: false, error: 'Store ID is required', data: null };

  return fetchData(TABLE_NAME, {
    filters: { store_id: storeId },
    single: true,
  });
}

/**
 * Tạo cấu hình chatbot mới
 * @param {Object} chatbotData - Dữ liệu cấu hình chatbot
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createChatbot(chatbotData) {
  return createData(TABLE_NAME, chatbotData);
}

/**
 * Cập nhật cấu hình chatbot
 * @param {string} chatbotId - ID của cấu hình chatbot
 * @param {Object} chatbotData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateChatbot(chatbotId, chatbotData) {
  if (!chatbotId) return { success: false, error: 'Chatbot ID is required', data: null };

  return updateData(TABLE_NAME, chatbotData, { id: chatbotId });
}

/**
 * Xóa cấu hình chatbot
 * @param {string} chatbotId - ID của cấu hình chatbot
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteChatbot(chatbotId) {
  if (!chatbotId) return { success: false, error: 'Chatbot ID is required', data: null };

  return deleteData(TABLE_NAME, { id: chatbotId });
}

/**
 * Tạo hoặc cập nhật cấu hình chatbot
 * @param {Object} chatbotData - Dữ liệu cấu hình chatbot
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertChatbot(chatbotData) {
  return upsertData(TABLE_NAME, chatbotData);
}

/**
 * Hook để lấy danh sách cấu hình chatbot
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Object} - Kết quả và các hàm điều khiển
 */
export function useChatbots(options = {}) {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  const fetchChatbots = useCallback(async () => {
    setIsValidating(true);
    try {
      const result = await getChatbots(options);
      setData(result);
      setError(result.error || null);
      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  }, [options]);

  // Hàm để tải lại dữ liệu
  const mutate = useCallback(async () => fetchChatbots(), [fetchChatbots]);

  // Tải dữ liệu khi component mount hoặc options thay đổi và khi tab trở nên visible
  useEffect(() => {
    setIsLoading(true);
    fetchChatbots();

    // Xử lý sự kiện khi tab trở nên visible
    const handleVisibilityChange = (event) => {
      const { visible, needsRefresh } = event.detail;
      if (visible && needsRefresh) {
        console.log('Tab became visible after inactivity, refreshing chatbots data');
        setIsValidating(true);
        fetchChatbots();
      }
    };

    // Đăng ký lắng nghe sự kiện
    window.addEventListener('app:visibilitychange', handleVisibilityChange);

    // Cleanup
    return () => {
      window.removeEventListener('app:visibilitychange', handleVisibilityChange);
    };
  }, [fetchChatbots]);

  const memoizedValue = useMemo(
    () => ({
      chatbots: data?.data || [],
      totalCount: data?.count || 0,
      isLoading,
      isError: !!error,
      error,
      isValidating,
      mutate,
      isEmpty: !isLoading && !isValidating && (!data?.data || data.data.length === 0),
    }),
    [data, error, isLoading, isValidating, mutate]
  );

  return memoizedValue;
}

/**
 * Hook để lấy chi tiết cấu hình chatbot
 * @param {string} chatbotId - ID của cấu hình chatbot
 * @returns {Object} - Kết quả và các hàm điều khiển
 */
export function useChatbot(chatbotId) {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  const fetchChatbot = useCallback(async () => {
    if (!chatbotId) {
      console.log('useChatbot: chatbotId is empty or null');
      setData(null);
      setError(null);
      setIsLoading(false);
      setIsValidating(false);
      return null;
    }

    console.log('useChatbot: Fetching chatbot with ID:', chatbotId);
    setIsValidating(true);
    try {
      const result = await getChatbotById(chatbotId);
      console.log('useChatbot: Fetch result:', result);
      setData(result);
      setError(result.error || null);
      return result;
    } catch (err) {
      console.error('useChatbot: Error fetching chatbot:', err);
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  }, [chatbotId]);

  // Hàm để tải lại dữ liệu
  const mutate = useCallback(async () => fetchChatbot(), [fetchChatbot]);

  // Tải dữ liệu khi component mount hoặc chatbotId thay đổi và khi tab trở nên visible
  useEffect(() => {
    if (chatbotId) {
      setIsLoading(true);
      fetchChatbot();

      // Xử lý sự kiện khi tab trở nên visible
      const handleVisibilityChange = (event) => {
        const { visible, needsRefresh } = event.detail;
        if (visible && needsRefresh && chatbotId) {
          console.log('Tab became visible after inactivity, refreshing chatbot data');
          setIsValidating(true);
          fetchChatbot();
        }
      };

      // Đăng ký lắng nghe sự kiện
      window.addEventListener('app:visibilitychange', handleVisibilityChange);

      // Cleanup
      return () => {
        window.removeEventListener('app:visibilitychange', handleVisibilityChange);
      };
    }
    // Không trả về gì khi điều kiện không thỏa mãn
  }, [fetchChatbot, chatbotId]);

  const memoizedValue = useMemo(() => {
    console.log('useChatbot memoizedValue:', {
      data,
      chatbot: data?.data || null,
      isLoading,
      isError: !!error,
      error,
      isValidating,
    });
    return {
      chatbot: data?.data || null,
      isLoading,
      isError: !!error,
      error,
      isValidating,
      mutate,
    };
  }, [data, error, isLoading, isValidating, mutate]);

  return memoizedValue;
}

/**
 * Hook để lấy cấu hình chatbot theo store ID
 * @param {string} storeId - ID của cửa hàng
 * @returns {Object} - Kết quả và các hàm điều khiển
 */
export function useChatbotByStore(storeId) {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  const fetchChatbotByStore = useCallback(async () => {
    if (!storeId) {
      setData(null);
      setError(null);
      setIsLoading(false);
      setIsValidating(false);
      return null;
    }

    setIsValidating(true);
    try {
      const result = await getChatbotByStoreId(storeId);
      setData(result);
      setError(result.error || null);
      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  }, [storeId]);

  // Hàm để tải lại dữ liệu
  const mutate = useCallback(async () => fetchChatbotByStore(), [fetchChatbotByStore]);

  // Tải dữ liệu khi component mount hoặc storeId thay đổi và khi tab trở nên visible
  useEffect(() => {
    if (storeId) {
      setIsLoading(true);
      fetchChatbotByStore();

      // Xử lý sự kiện khi tab trở nên visible
      const handleVisibilityChange = (event) => {
        const { visible, needsRefresh } = event.detail;
        if (visible && needsRefresh && storeId) {
          console.log('Tab became visible after inactivity, refreshing store chatbot data');
          setIsValidating(true);
          fetchChatbotByStore();
        }
      };

      // Đăng ký lắng nghe sự kiện
      window.addEventListener('app:visibilitychange', handleVisibilityChange);

      // Cleanup
      return () => {
        window.removeEventListener('app:visibilitychange', handleVisibilityChange);
      };
    }
    // No return needed when condition is not met
  }, [fetchChatbotByStore, storeId]);

  const memoizedValue = useMemo(
    () => ({
      chatbot: data?.data || null,
      isLoading,
      isError: !!error,
      error,
      isValidating,
      mutate,
    }),
    [data, error, isLoading, isValidating, mutate]
  );

  return memoizedValue;
}

/**
 * Hook để tạo, cập nhật, xóa cấu hình chatbot
 * @returns {Object} - Các hàm mutation
 */
export function useChatbotMutations() {
  const [loadingStates, setLoadingStates] = useState({
    creating: false,
    updating: false,
    deleting: false,
    upserting: false,
    uploadingAvatar: false,
    deletingAvatar: false,
  });

  // Helper function to handle loading state and error handling
  const withLoadingState = async (stateKey, asyncFn) => {
    setLoadingStates((prev) => ({ ...prev, [stateKey]: true }));
    try {
      return await asyncFn();
    } finally {
      setLoadingStates((prev) => ({ ...prev, [stateKey]: false }));
    }
  };

  // Mutation functions using the helper
  const createChatbotMutation = (chatbotData) =>
    withLoadingState('creating', () => createChatbot(chatbotData));

  const updateChatbotMutation = (id, data) =>
    withLoadingState('updating', () => updateChatbot(id, data));

  const deleteChatbotMutation = (chatbotId) =>
    withLoadingState('deleting', () => deleteChatbot(chatbotId));

  const upsertChatbotMutation = (chatbotData) =>
    withLoadingState('upserting', () => upsertChatbot(chatbotData));

  /**
   * Xử lý tải lên avatar chatbot
   * @param {File} avatarFile - File avatar cần tải lên
   * @param {string|null} oldAvatarUrl - URL avatar cũ (nếu có)
   * @returns {Promise<Object>} - Kết quả từ API với URL avatar
   */
  const uploadChatbotAvatarMutation = async (avatarFile, oldAvatarUrl) =>
    withLoadingState('uploadingAvatar', async () => {
      try {
        if (!avatarFile) {
          return { success: false, error: 'Missing required file', avatarUrl: null };
        }

        // Xóa avatar cũ nếu có
        if (oldAvatarUrl) {
          await deleteChatbotAvatarMutation(oldAvatarUrl);
        }

        // Lấy tenant_id từ middleware (sẽ được xử lý tự động)
        const { getCachedTenantId } = await import('./tenant-middleware');
        const tenantId = await getCachedTenantId();

        if (!tenantId) {
          return { success: false, error: 'Could not determine tenant ID', avatarUrl: null };
        }

        // Tạo tên file duy nhất
        const fileName = storageService.generateUniqueFileName(avatarFile.name);
        // Tạo đường dẫn lưu trữ
        const filePath = storageService.buildFilePath('chatbots', tenantId, fileName);

        console.log('Uploading new avatar to path:', filePath);
        // Tải lên avatar mới
        const uploadResult = await storageService.uploadFile('public', filePath, avatarFile, {
          upsert: true,
          cacheControl: '3600',
        });

        if (uploadResult.success) {
          console.log('Avatar uploaded successfully, public URL:', uploadResult.publicUrl);
          return { success: true, avatarUrl: uploadResult.publicUrl };
        }

        console.error('Failed to upload avatar:', uploadResult.error);
        return {
          success: false,
          error: uploadResult.error,
          avatarUrl: null,
        };
      } catch (error) {
        console.error('Error uploading avatar:', error);
        return { success: false, error, avatarUrl: null };
      }
    });

  /**
   * Xóa avatar chatbot
   * @param {string} avatarUrl - URL avatar cần xóa
   * @returns {Promise<Object>} - Kết quả từ API
   */
  const deleteChatbotAvatarMutation = (avatarUrl) =>
    withLoadingState('deletingAvatar', async () => {
      try {
        if (!avatarUrl) return { success: true };

        console.log('Deleting avatar with URL:', avatarUrl);

        // Trích xuất đường dẫn từ URL
        const avatarPath = storageService.extractPathFromUrl(avatarUrl);
        if (!avatarPath) {
          console.warn('Could not extract path from avatar URL:', avatarUrl);
          return { success: false, error: 'Invalid avatar URL format' };
        }

        // Kiểm tra xem đường dẫn có chứa 'public/' ở đầu không
        const cleanPath = avatarPath.startsWith('public/')
          ? avatarPath.substring(7) // Cắt bỏ 'public/' ở đầu nếu có
          : avatarPath;

        console.log('Deleting avatar at path:', cleanPath);

        // Xóa file từ storage
        const deleteResult = await storageService.deleteFiles('public', cleanPath);

        if (deleteResult.success) {
          console.log('Avatar deleted successfully');
          return { success: true };
        }

        console.error('Failed to delete avatar:', deleteResult.error);
        return { success: false, error: deleteResult.error };
      } catch (error) {
        console.error('Error deleting avatar:', error);
        return { success: false, error };
      }
    });

  // Calculate if any mutation is in progress
  const isMutating = Object.values(loadingStates).some(Boolean);

  return {
    createChatbot: createChatbotMutation,
    updateChatbot: updateChatbotMutation,
    deleteChatbot: deleteChatbotMutation,
    upsertChatbot: upsertChatbotMutation,
    uploadChatbotAvatar: uploadChatbotAvatarMutation,
    deleteChatbotAvatar: deleteChatbotAvatarMutation,
    isCreating: loadingStates.creating,
    isUpdating: loadingStates.updating,
    isDeleting: loadingStates.deleting,
    isUpserting: loadingStates.upserting,
    isMutating,
  };
}
