'use client';

import { createClient } from 'src/utils/supabase/client';

/**
 * D<PERSON><PERSON> vụ xử lý lưu trữ hình ảnh và tệp tin với Supabase Storage
 * Cung cấp các chức năng tải lên, t<PERSON><PERSON> xu<PERSON>, x<PERSON><PERSON>, tạo URL công khai, v.v.
 */
export const storageService = {
  /**
   * Kiểm tra và đảm bảo bucket tồn tại
   * @param {string} bucketName - Tên bucket cần kiểm tra
   * @returns {Promise<Object>} - Kết quả kiểm tra
   */
  async ensureBucketExists(bucketName) {
    try {
      const supabase = createClient();

      // Kiểm tra bucket có tồn tại không
      const { data, error } = await supabase.storage.getBucket(bucketName);

      if (error) {
        // Nếu bucket không tồn tại, thử tạo mới
        if (error.code === 'PGRST116' || error.message.includes('not found')) {
          const { data: newBucket, error: createError } = await supabase.storage.createBucket(
            bucketName,
            {
              public: true,
            }
          );

          if (createError) {
            return { success: false, error: createError };
          }

          return { success: true, data: newBucket };
        } else {
          return { success: false, error };
        }
      }

      return { success: true, data };
    } catch (error) {
      return { success: false, error };
    }
  },

  /**
   * Tải lên một tệp tin
   * @param {string} bucket - Tên bucket lưu trữ
   * @param {string} path - Đường dẫn lưu trữ (bao gồm tên tệp)
   * @param {File|Blob|ArrayBuffer} fileBody - Tệp tin cần tải lên
   * @param {Object} options - Tùy chọn tải lên
   * @param {string} options.cacheControl - Thời gian cache (ví dụ: '3600')
   * @param {boolean} options.upsert - Ghi đè tệp nếu đã tồn tại
   * @param {string} options.contentType - Loại nội dung (ví dụ: 'image/png')
   * @returns {Promise<Object>} - Kết quả từ Supabase
   */
  async uploadFile(bucket, path, fileBody, options = {}) {
    try {
      // Đảm bảo bucket tồn tại
      const bucketCheck = await this.ensureBucketExists(bucket);
      if (!bucketCheck.success) {
        return { success: false, error: bucketCheck.error, data: null, path: null };
      }

      // Kiểm tra xem người dùng đã đăng nhập chưa
      const supabase = createClient();
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        return {
          success: false,
          error: { message: 'Người dùng chưa đăng nhập hoặc phiên đăng nhập đã hết hạn' },
          data: null,
          path: null
        };
      }

      // Thực hiện tải lên với các tùy chọn
      const { data, error } = await supabase.storage.from(bucket).upload(path, fileBody, {
        cacheControl: options.cacheControl || '3600',
        upsert: options.upsert !== undefined ? options.upsert : false,
        contentType: options.contentType || undefined,
      });

      if (error) {
        console.error('Upload error:', error);
        return { success: false, error, data: null, path: null };
      }

      // Tạo URL công khai nếu tải lên thành công
      const publicUrl = this.getPublicUrl(bucket, path);

      return {
        success: true,
        data,
        error: null,
        path,
        publicUrl,
      };
    } catch (error) {
      console.error('Exception during upload:', error);
      return { success: false, error, data: null, path: null, publicUrl: null };
    }
  },

  /**
   * Tải lên nhiều tệp tin
   * @param {string} bucket - Tên bucket lưu trữ
   * @param {Array<{path: string, file: File|Blob|ArrayBuffer, options: Object}>} files - Mảng các tệp tin cần tải lên
   * @returns {Promise<Object>} - Kết quả từ Supabase
   */
  async uploadMultipleFiles(bucket, files) {
    try {
      const results = await Promise.all(
        files.map(async ({ path, file, options = {} }) => {
          const result = await this.uploadFile(bucket, path, file, options);
          return {
            path,
            success: result.success,
            data: result.data,
            error: result.error,
            publicUrl: result.publicUrl,
          };
        })
      );

      const failedUploads = results.filter((result) => !result.success);

      return {
        success: failedUploads.length === 0,
        data: results,
        error: failedUploads.length > 0 ? 'Some files failed to upload' : null,
      };
    } catch (error) {
      return { success: false, error, data: null };
    }
  },

  /**
   * Tải xuống một tệp tin
   * @param {string} bucket - Tên bucket lưu trữ
   * @param {string} path - Đường dẫn lưu trữ (bao gồm tên tệp)
   * @returns {Promise<Object>} - Kết quả từ Supabase
   */
  async downloadFile(bucket, path) {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.storage.from(bucket).download(path);

      if (error) {
        return { success: false, error, data: null };
      }

      return { success: true, data, error: null };
    } catch (error) {
      return { success: false, error, data: null };
    }
  },

  /**
   * Xóa một hoặc nhiều tệp tin
   * @param {string} bucket - Tên bucket lưu trữ
   * @param {string|Array<string>} paths - Đường dẫn hoặc mảng các đường dẫn cần xóa
   * @returns {Promise<Object>} - Kết quả từ Supabase
   */
  async deleteFiles(bucket, paths) {
    try {
      const pathsArray = Array.isArray(paths) ? paths : [paths];
      // Lọc bỏ các đường dẫn rỗng hoặc không hợp lệ
      const validPaths = pathsArray.filter(
        (path) => path && typeof path === 'string' && path.trim() !== ''
      );

      if (validPaths.length === 0) {
        return { success: true, data: [], error: null };
      }

      // Loại bỏ phần "bucket/" ở đầu đường dẫn nếu có
      const cleanedPaths = validPaths.map((path) => {
        // Kiểm tra nếu đường dẫn bắt đầu bằng "bucket/"
        if (path.startsWith(`${bucket}/`)) {
          // Cắt bỏ phần "bucket/" ở đầu
          const cleanPath = path.substring(bucket.length + 1);
          return cleanPath;
        }
        return path;
      });

      const supabase = createClient();
      const { data, error } = await supabase.storage.from(bucket).remove(cleanedPaths);

      if (error) {
        return { success: false, error, data: null };
      }

      return { success: true, data, error: null };
    } catch (error) {
      return { success: false, error, data: null };
    }
  },

  /**
   * Xóa các tệp tin dựa trên URL công khai
   * @param {string} bucket - Tên bucket lưu trữ
   * @param {string|Array<string>} publicUrls - URL công khai hoặc mảng các URL công khai cần xóa
   * @returns {Promise<Object>} - Kết quả từ Supabase
   */
  async deleteFilesWithPublicUrl(bucket, publicUrls) {
    try {
      const urlsArray = Array.isArray(publicUrls) ? publicUrls : [publicUrls];

      // Lọc bỏ các URL rỗng hoặc không hợp lệ
      const validUrls = urlsArray.filter(
        (url) => url && typeof url === 'string' && url.trim() !== ''
      );

      if (validUrls.length === 0) {
        return { success: true, data: [], error: null };
      }

      // Trích xuất đường dẫn từ URL công khai
      const pathsToDelete = [];
      const failedUrls = [];

      for (const url of validUrls) {
        const path = this.extractPathFromUrl(url, bucket);
        if (path) {
          pathsToDelete.push(path);
        } else {
          failedUrls.push(url);
        }
      }

      if (pathsToDelete.length === 0) {
        return {
          success: false,
          error: 'Could not extract valid paths from URLs',
          data: null,
          failedUrls: validUrls,
        };
      }

      // Xóa các tệp tin bằng đường dẫn đã trích xuất
      const deleteResult = await this.deleteFiles(bucket, pathsToDelete);

      // Thêm thông tin về các URL không thể xử lý
      return {
        ...deleteResult,
        processedUrls: validUrls.length,
        successfullyProcessed: pathsToDelete.length,
        failedUrls,
      };
    } catch (error) {
      return { success: false, error, data: null };
    }
  },

  /**
   * Lấy danh sách tệp tin trong một bucket
   * @param {string} bucket - Tên bucket lưu trữ
   * @param {string} path - Đường dẫn thư mục (mặc định là gốc)
   * @param {Object} options - Tùy chọn liệt kê
   * @param {string} options.sortBy - Tiêu chí sắp xếp (name, createdAt, updatedAt)
   * @param {string} options.order - Thứ tự sắp xếp (asc, desc)
   * @returns {Promise<Object>} - Kết quả từ Supabase
   */
  async listFiles(bucket, path = '', options = {}) {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.storage.from(bucket).list(path, {
        sortBy: options.sortBy,
        search: options.search,
        limit: options.limit,
        offset: options.offset,
      });

      if (error) {
        return { success: false, error, data: null };
      }

      // Thêm URL công khai cho mỗi tệp
      const filesWithUrls = data.map((file) => {
        const filePath = path ? `${path}/${file.name}` : file.name;
        return {
          ...file,
          publicUrl: this.getPublicUrl(bucket, filePath),
        };
      });

      return { success: true, data: filesWithUrls, error: null };
    } catch (error) {
      return { success: false, error, data: null };
    }
  },

  /**
   * Lấy URL công khai của một tệp tin
   * @param {string} bucket - Tên bucket lưu trữ
   * @param {string} path - Đường dẫn tệp tin
   * @param {Object} options - Tùy chọn bổ sung
   * @param {Object} options.transform - Tùy chọn biến đổi hình ảnh
   * @param {number} options.transform.width - Chiều rộng hình ảnh
   * @param {number} options.transform.height - Chiều cao hình ảnh
   * @param {string} options.transform.resize - Chế độ resize ('cover', 'contain', 'fill')
   * @returns {string} URL công khai
   */
  getPublicUrl(bucket, path, options = {}) {
    if (!path || typeof path !== 'string' || path.trim() === '') {
      return null;
    }

    try {
      const supabase = createClient();
      // Sử dụng API của Supabase để lấy URL công khai
      const { data } = supabase.storage.from(bucket).getPublicUrl(path, options);

      return data?.publicUrl || null;
    // eslint-disable-next-line no-unused-vars
    } catch (error) {
      return null;
    }
  },

  /**
   * Lấy URL công khai của một hình ảnh sản phẩm
   * @param {string} bucket - Tên bucket lưu trữ (mặc định là 'public')
   * @param {string} moduleType - Loại module (mặc định là 'products')
   * @param {string} entityId - ID của thực thể (ví dụ: tenantId, productId)
   * @param {string} fileName - Tên file
   * @param {Object} options - Tùy chọn biến đổi hình ảnh
   * @returns {string} URL công khai
   */
  getProductImageUrl(bucket = 'public', moduleType = 'products', entityId, fileName, options = {}) {
    if (!entityId || !fileName) {
      return null;
    }

    // Rút gọn entityId để tránh đường dẫn quá dài
    const shortId = entityId.substring(0, 8);

    // Tạo đường dẫn đầy đủ
    const filePath = `${moduleType}/${shortId}/${fileName}`;

    return this.getPublicUrl(bucket, filePath, options);
  },

  /**
   * Lấy URL công khai của một hình ảnh từ mediaId
   * @param {string} bucket - Tên bucket lưu trữ (mặc định là 'public')
   * @param {string} moduleType - Loại module (mặc định là 'products')
   * @param {string} tenantId - ID của tenant
   * @param {string} mediaId - ID của media
   * @param {Object} options - Tùy chọn biến đổi hình ảnh
   * @returns {string} URL công khai
   */
  getMediaUrl(bucket = 'public', moduleType = 'products', tenantId, mediaId, options = {}) {
    if (!tenantId || !mediaId) {
      return null;
    }

    // Tạo tên file từ mediaId
    const fileName = `${mediaId}.jpg`;

    return this.getProductImageUrl(bucket, moduleType, tenantId, fileName, options);
  },

  /**
   * Tạo URL đã ký để tải tệp tin lên (sử dụng cho tải lên phía client)
   * @param {string} bucket - Tên bucket lưu trữ
   * @param {string} path - Đường dẫn tệp tin
   * @param {Object} options - Tùy chọn
   * @returns {Promise<Object>} - Kết quả từ Supabase
   */
  async createSignedUploadUrl(bucket, path, options = {}) {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.storage
        .from(bucket)
        .createSignedUploadUrl(path, options);

      if (error) {
        return { success: false, error, data: null };
      }

      return { success: true, data, error: null };
    } catch (error) {
      return { success: false, error, data: null };
    }
  },

  /**
   * Tạo URL đã ký để truy cập tệp tin (có thời hạn)
   * @param {string} bucket - Tên bucket lưu trữ
   * @param {string} path - Đường dẫn tệp tin
   * @param {number} expiresIn - Thời hạn URL (giây)
   * @param {Object} options - Tùy chọn bổ sung
   * @param {Object} options.transform - Tùy chọn biến đổi hình ảnh
   * @param {number} options.transform.width - Chiều rộng hình ảnh
   * @param {number} options.transform.height - Chiều cao hình ảnh
   * @param {string} options.transform.resize - Chế độ resize ('cover', 'contain', 'fill')
   * @returns {Promise<Object>} - Kết quả từ Supabase
   */
  async createSignedUrl(bucket, path, expiresIn = 60, options = {}) {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.storage
        .from(bucket)
        .createSignedUrl(path, expiresIn, options);

      if (error) {
        return { success: false, error, data: null };
      }

      return { success: true, data, error: null };
    } catch (error) {
      return { success: false, error, data: null };
    }
  },

  /**
   * Di chuyển tệp tin từ vị trí cũ sang vị trí mới
   * @param {string} bucket - Tên bucket lưu trữ
   * @param {string} fromPath - Đường dẫn hiện tại
   * @param {string} toPath - Đường dẫn mới
   * @returns {Promise<Object>} - Kết quả từ Supabase
   */
  async moveFile(bucket, fromPath, toPath) {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.storage.from(bucket).move(fromPath, toPath);

      if (error) {
        return { success: false, error, data: null };
      }

      const publicUrl = this.getPublicUrl(bucket, toPath);
      return { success: true, data, error: null, publicUrl };
    } catch (error) {
      return { success: false, error, data: null };
    }
  },

  /**
   * Sao chép tệp tin từ vị trí này sang vị trí khác
   * @param {string} bucket - Tên bucket lưu trữ
   * @param {string} fromPath - Đường dẫn nguồn
   * @param {string} toPath - Đường dẫn đích
   * @returns {Promise<Object>} - Kết quả từ Supabase
   */
  async copyFile(bucket, fromPath, toPath) {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.storage.from(bucket).copy(fromPath, toPath);

      if (error) {
        return { success: false, error, data: null };
      }

      const publicUrl = this.getPublicUrl(bucket, toPath);
      return { success: true, data, error: null, publicUrl };
    } catch (error) {
      return { success: false, error, data: null };
    }
  },

  /**
   * Kiểm tra sự tồn tại của tệp tin
   * @param {string} bucket - Tên bucket lưu trữ
   * @param {string} path - Đường dẫn tệp tin
   * @returns {Promise<Object>} - Kết quả kiểm tra
   */
  async checkFileExists(bucket, path) {
    try {
      if (!path || typeof path !== 'string' || path.trim() === '') {
        return { success: false, error: 'Invalid path', exists: false };
      }

      // Cách tốt nhất để kiểm tra tệp tồn tại là thử liệt kê thư mục chứa tệp
      // và tìm kiếm tệp bằng tên
      const pathParts = path.split('/');
      const fileName = pathParts.pop();
      const directory = pathParts.join('/');

      if (!fileName) {
        return { success: false, error: 'Invalid file path format', exists: false };
      }

      const supabase = createClient();
      const { data, error } = await supabase.storage.from(bucket).list(directory, {
        search: fileName,
      });

      if (error) {
        return { success: false, error, exists: false };
      }

      const exists = data.some((file) => file.name === fileName);
      return { success: true, error: null, exists };
    } catch (error) {
      return { success: false, error, exists: false };
    }
  },

  /**
   * Tạo tên tệp duy nhất bằng cách thêm dấu thời gian
   * @param {string} originalFileName - Tên tệp gốc
   * @returns {string} Tên tệp duy nhất
   */
  generateUniqueFileName(originalFileName) {
    // Xử lý trường hợp originalFileName là undefined hoặc null
    if (!originalFileName) {
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substring(2, 8);
      return `img_${timestamp}_${randomStr}.jpg`;
    }

    // Lấy phần mở rộng của file
    const parts = originalFileName.split('.');
    const extension = parts.length > 1 ? parts.pop() : 'jpg';

    // Tạo tên file ngắn hơn và không có ký tự đặc biệt
    // Sử dụng timestamp để đảm bảo tên file duy nhất
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 8);

    return `img_${timestamp}_${randomStr}.${extension}`;
  },

  /**
   * Tạo đường dẫn tệp tin với cấu trúc thư mục tổ chức
   * @param {string} moduleName - Tên module (ví dụ: 'products', 'users', 'chatbot')
   * @param {string|null} entityId - ID của thực thể liên quan (ví dụ: ID người dùng, ID sản phẩm)
   * @param {string} fileName - Tên tệp tin
   * @returns {string} Đường dẫn đầy đủ cho tệp tin
   */
  buildFilePath(moduleName, entityId, fileName) {
    if (!moduleName || !fileName) {
      throw new Error('Missing required parameters for buildFilePath');
    }

    // Nếu entityId là null hoặc undefined, sử dụng 'shared' làm thư mục
    if (!entityId) {
      return `${moduleName}/shared/${fileName}`;
    }

    // Rút gọn đường dẫn để tránh lỗi "Invalid key"
    // Cấu trúc: module/ID/tên_tệp

    // Lấy 8 ký tự đầu của entityId để làm thư mục
    // Tránh đường dẫn quá dài
    const shortId = entityId.substring(0, 8);

    // Đảm bảo cấu trúc đường dẫn chính xác:
    // https://[project_ref].supabase.co/storage/v1/object/public/public/products/[tenant_id_short]/[filename]
    return `${moduleName}/${shortId}/${fileName}`;
  },

  /**
   * Trích xuất thông tin của tệp hình ảnh (kích thước, loại, v.v.)
   * @param {File|Blob} imageFile - Tệp hình ảnh
   * @returns {Promise<Object>} - Thông tin hình ảnh
   */
  async getImageInfo(imageFile) {
    if (!imageFile) {
      return Promise.reject(new Error('No image file provided'));
    }

    return new Promise((resolve, reject) => {
      const img = new Image();
      const objectUrl = URL.createObjectURL(imageFile);

      img.onload = () => {
        const info = {
          width: img.width,
          height: img.height,
          aspectRatio: img.width / img.height,
          size: imageFile.size,
          type: imageFile.type,
          name: imageFile.name,
        };
        URL.revokeObjectURL(objectUrl);
        resolve(info);
      };

      img.onerror = (error) => {
        URL.revokeObjectURL(objectUrl);
        reject(error);
      };

      img.src = objectUrl;
    });
  },

  /**
   * Trích xuất đường dẫn tệp tin từ URL công khai
   * @param {string} publicUrl - URL công khai của tệp tin
   * @param {string} bucket - Tên bucket (mặc định là 'public')
   * @returns {string|null} - Đường dẫn tệp tin hoặc null nếu không thể trích xuất
   */
  extractPathFromUrl(publicUrl, bucket = 'public') {
    if (!publicUrl || typeof publicUrl !== 'string') {
      return null;
    }

    try {
      // Phương pháp 1: Sử dụng regex để trích xuất đường dẫn
      // Tìm kiếm mẫu "bucket/path/to/file.ext"
      const regex = new RegExp(`${bucket}/([^?#]+)`);
      const match = publicUrl.match(regex);

      if (match && match[1]) {
        return match[1];
      }

      // Phương pháp 2: Tách chuỗi (phương pháp dự phòng)
      const parts = publicUrl.split(`${bucket}/`);
      if (parts.length > 1) {
        // Loại bỏ các tham số truy vấn nếu có
        const path = parts[1].split('?')[0].split('#')[0];
        return path;
      }

      return null;
    // eslint-disable-next-line no-unused-vars
    } catch (error) {
      return null;
    }
  },
};

// Export default để tương thích với cả nhập khẩu mặc định và có tên
export default storageService;
