'use client';

/**
 * <PERSON><PERSON><PERSON> hằng số và cấu hình cho module credit
 */

// Tên bảng trong Supabase
export const CREDIT_PACKAGES_TABLE = 'credit_packages';
export const TENANT_CREDITS_TABLE = 'tenant_credits';
export const CREDIT_TRANSACTIONS_TABLE = 'credit_transactions';
export const CREDIT_PAYMENTS_TABLE = 'credit_payments';

// Cấu hình mặc định
export const DEFAULT_CREDIT_OPTIONS = {
  orderBy: 'createdAt',
  ascending: false,
};

// Loại giao dịch credit
export const CREDIT_TRANSACTION_TYPES = {
  PURCHASE: 'purchase',
  USAGE: 'usage',
  REFUND: 'refund',
  BONUS: 'bonus',
  EXPIRATION: 'expiration',
};

// Trạng thái thanh toán
export const PAYMENT_STATUSES = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  REFUNDED: 'refunded',
};

// Phương thức thanh toán
export const PAYMENT_METHODS = {
  PAYOS: 'payos',
  BANK_TRANSFER: 'bank_transfer',
  MANUAL: 'manual',
};

// Tên trường trong database - đồng bộ chính xác với schema DB
export const CREDIT_PACKAGE_FIELDS = {
  ID: 'id',
  NAME: 'name',
  DESCRIPTION: 'description',
  CREDIT_AMOUNT: 'creditAmount',
  PRICE: 'price',
  DISCOUNT_PERCENTAGE: 'discountPercentage',
  IS_ACTIVE: 'isActive',
  SORT_ORDER: 'sortOrder',
  CREATED_AT: 'createdAt',
  UPDATED_AT: 'updatedAt',
};

export const TENANT_CREDIT_FIELDS = {
  ID: 'id',
  TENANT_ID: 'tenantId',
  BALANCE: 'balance',
  LAST_UPDATED: 'lastUpdated',
  CREATED_AT: 'createdAt',
  UPDATED_AT: 'updatedAt',
};

export const CREDIT_TRANSACTION_FIELDS = {
  ID: 'id',
  TENANT_ID: 'tenantId',
  AMOUNT: 'amount',
  BALANCE_BEFORE: 'balanceBefore',
  BALANCE_AFTER: 'balanceAfter',
  TYPE: 'type',
  DESCRIPTION: 'description',
  REFERENCE_ID: 'referenceId',
  REFERENCE_TYPE: 'referenceType',
  CREATED_BY: 'createdBy',
  CREATED_AT: 'createdAt',
};

export const CREDIT_PAYMENT_FIELDS = {
  ID: 'id',
  TENANT_ID: 'tenantId',
  PACKAGE_ID: 'packageId',
  AMOUNT: 'amount',
  CREDIT_AMOUNT: 'creditAmount',
  PAYMENT_METHOD: 'paymentMethod',
  PAYMENT_STATUS: 'paymentStatus',
  PAYMENT_ID: 'paymentId',
  PAYMENT_DATA: 'paymentData',
  TRANSACTION_ID: 'transactionId',
  ORDER_CODE: 'orderCode',
  CREATED_BY: 'createdBy',
  CREATED_AT: 'createdAt',
  UPDATED_AT: 'updatedAt',
};
