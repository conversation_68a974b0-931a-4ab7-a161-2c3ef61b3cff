'use client';

/**
 * Service để đồng bộ sản phẩm từ các nền tảng khác
 */
export const platformSyncService = {
  /**
   * Đồng bộ sản phẩm từ <PERSON>van
   * @param {Object} params - Tham số đồng bộ
   * @param {string} params.token - Token xác thực <PERSON>
   * @param {number} params.limit - Số lượng sản phẩm tối đa cần đồng bộ
   * @param {boolean} params.full_sync - Đồng bộ toàn bộ hay chỉ đồng bộ sản phẩm mới
   * @returns {Promise<Object>} - Kết quả đồng bộ
   */
  syncHaravanProducts: async ({ token, limit = 50, full_sync = true }) => {
    try {
      if (!token) {
        return { success: false, error: 'Token is required' };
      }

      const response = await fetch('/api/platform-sync/haravan/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          limit,
          full_sync,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.error || 'Failed to sync products from Haravan',
        };
      }

      return data;
    } catch (error) {
      console.error('Error syncing products from Haravan:', error);
      return {
        success: false,
        error: error.message || 'Failed to sync products from Haravan',
      };
    }
  },

  /**
   * Đồng bộ sản phẩm từ Sapo
   * @param {Object} params - Tham số đồng bộ
   * @param {string} params.sapo_url - URL API Sapo
   * @param {number} params.limit - Số lượng sản phẩm tối đa cần đồng bộ
   * @param {boolean} params.full_sync - Đồng bộ toàn bộ hay chỉ đồng bộ sản phẩm mới
   * @returns {Promise<Object>} - Kết quả đồng bộ
   */
  syncSapoProducts: async ({ sapo_url, limit = 50, full_sync = true }) => {
    try {
      if (!sapo_url) {
        return { success: false, error: 'Sapo URL is required' };
      }

      const response = await fetch('/api/platform-sync/sapo/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sapo_url,
          limit,
          full_sync,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.error || 'Failed to sync products from Sapo',
        };
      }

      return data;
    } catch (error) {
      console.error('Error syncing products from Sapo:', error);
      return {
        success: false,
        error: error.message || 'Failed to sync products from Sapo',
      };
    }
  },
};
