'use client';

import axios from 'axios';

import { getPaymentById, updatePaymentData } from './credit-service';

/**
 * Tạo link thanh toán PayOS
 * @param {Object} paymentData - D<PERSON> liệu thanh toán
 * @returns {Promise<Object>} - <PERSON><PERSON><PERSON> quả từ API PayOS
 */
export async function createPaymentLink(paymentData) {
  try {
    // Kiểm tra xem có order_code không (từ bảng credit_payments)
    if (!paymentData.orderCode) {
      return {
        success: false,
        error: 'orderCode is required',
        data: null,
      };
    }

    // Giới hạn mô tả tối đa 25 ký tự theo yêu cầu của PayOS
    const shortDescription = (paymentData.description || '').slice(0, 25);

    const requestData = {
      orderCode: paymentData.orderCode, // Sử dụng order_code từ bảng credit_payments
      amount: parseInt(paymentData.amount, 10), // Đảm bảo amount là số nguyên
      description: shortDescription,
      returnUrl: paymentData.returnUrl,
      cancelUrl: paymentData.cancelUrl,
    };

    // Thêm các trường tùy chọn nếu có
    if (paymentData.buyerName) requestData.buyerName = paymentData.buyerName;
    if (paymentData.buyerEmail) requestData.buyerEmail = paymentData.buyerEmail;
    if (paymentData.buyerPhone) requestData.buyerPhone = paymentData.buyerPhone;
    if (paymentData.buyerAddress) requestData.buyerAddress = paymentData.buyerAddress;
    if (paymentData.items && Array.isArray(paymentData.items)) requestData.items = paymentData.items;

    // Thêm thời gian hết hạn nếu có (Unix timestamp)
    if (paymentData.expiredAt) {
      requestData.expiredAt = paymentData.expiredAt;
    } else if (paymentData.expiryTime) {
      // Nếu có expiryTime (số giờ), tính toán Unix timestamp
      const expiryHours = parseInt(paymentData.expiryTime, 10) || 24; // Mặc định 24 giờ
      const expiredAt = Math.floor(Date.now() / 1000) + expiryHours * 3600;
      requestData.expiredAt = expiredAt;
    }

    // Gọi API route server-side để tạo link thanh toán
    const response = await axios.post('/api/payos/create-payment', requestData);

    // Nếu thành công, cập nhật payment_data trong bảng credit_payments
    if (response.data && response.data.success && response.data.data) {
      // Lấy thông tin thanh toán từ database
      const { data: payment } = await getPaymentById(paymentData.paymentId);

      if (payment) {
        // Cập nhật payment_data với thông tin từ PayOS
        await updatePaymentData(payment.id, {
          paymentData: response.data.data
        });

        console.log(`Payment created: PayOS ID ${requestData.orderCode}, Payment ID: ${payment.id}`);
      }
    }

    return response.data;
  } catch (error) {
    console.error('PayOS payment link creation error:', error);
    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to create payment link',
      data: null,
    };
  }
}

/**
 * Lấy thông tin thanh toán từ PayOS
 * @param {string} orderId - Mã đơn hàng hoặc ID thanh toán
 * @returns {Promise<Object>} - Kết quả từ API PayOS
 */
export async function getPaymentInfo(orderId) {
  try {
    if (!orderId) {
      return {
        success: false,
        error: 'Order ID is required',
        data: null,
      };
    }

    // Gọi API route server-side để lấy thông tin thanh toán
    const response = await axios.get(`/api/payos/get-payment-info?orderId=${orderId}`);

    return response.data;
  } catch (error) {
    console.error('PayOS get payment info error:', error);
    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to get payment information',
      data: null,
    };
  }
}

/**
 * Xác minh dữ liệu webhook từ PayOS
 * @param {Object} webhookData - Dữ liệu webhook từ PayOS
 * @returns {Object} - Dữ liệu đã xác minh
 * @deprecated Sử dụng hàm verifyWebhookData từ src/utils/payos-utils.js thay thế
 */
export function verifyWebhookData(webhookData) {
  console.warn('This function is deprecated. Use verifyWebhookData from src/utils/payos-utils.js instead.');

  try {
    // Kiểm tra dữ liệu webhook
    if (!webhookData || !webhookData.data) {
      return {
        success: false,
        error: 'Invalid webhook data',
        data: null,
      };
    }

    // Trả về dữ liệu đã xác minh
    // Lưu ý: Hàm này không xác minh chữ ký, chỉ kiểm tra cấu trúc dữ liệu
    return {
      success: true,
      data: webhookData.data,
      error: null,
    };
  } catch (error) {
    console.error('PayOS webhook verification error:', error);
    return {
      success: false,
      error: error.message || 'Failed to verify webhook data',
      data: null,
    };
  }
}

/**
 * Hủy link thanh toán PayOS
 * @param {string} orderId - Mã đơn hàng hoặc ID thanh toán
 * @param {string} cancellationReason - Lý do hủy
 * @returns {Promise<Object>} - Kết quả từ API PayOS
 */
export async function cancelPaymentLink(orderId, cancellationReason = '') {
  try {
    if (!orderId) {
      return {
        success: false,
        error: 'Order ID is required',
        data: null,
      };
    }

    // Gọi API route server-side để hủy link thanh toán
    const response = await axios.post('/api/payos/cancel-payment', {
      orderId,
      cancellationReason,
    });

    return response.data;
  } catch (error) {
    console.error('PayOS cancel payment link error:', error);
    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to cancel payment link',
      data: null,
    };
  }
}
