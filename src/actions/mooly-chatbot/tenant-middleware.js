'use client';

import { createClient } from 'src/utils/supabase/client';

/**
 * Lấy tenant_id của người dùng hiện tại
 * @returns {Promise<string|null>} - tenant_id hoặc null nếu không tìm thấy
 */
export async function getCurrentUserTenantId() {
  try {
    const supabase = createClient();

    // Lấy thông tin người dùng hiện tại
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) return null;

    // Lấy tenant_id từ bảng users
    const { data, error } = await supabase
      .from('users')
      .select('tenant_id')
      .eq('id', user.id)
      .single();

    if (error) throw error;
    return data?.tenant_id || null;
  } catch (err) {
    console.error('Error getting current user tenant ID:', err);
    return null;
  }
}

// Cache tenant_id để tránh gọi API nhiều lần
let cachedTenantId = null;
let cacheExpiry = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 phút

/**
 * Lấy tenant_id của người dùng hiện tại (có cache)
 * @returns {Promise<string|null>} - tenant_id hoặc null nếu không tìm thấy
 */
export async function getCachedTenantId() {
  const now = Date.now();

  // Nếu cache còn hiệu lực, trả về giá trị đã cache
  if (cachedTenantId && now < cacheExpiry) {
    return cachedTenantId;
  }

  // Nếu không, lấy tenant_id mới và cập nhật cache
  const tenantId = await getCurrentUserTenantId();
  if (tenantId) {
    cachedTenantId = tenantId;
    cacheExpiry = now + CACHE_DURATION;
  } else {
    cachedTenantId = null;
  }

  return cachedTenantId;
}

/**
 * Xóa cache tenant_id
 */
export function clearTenantIdCache() {
  cachedTenantId = null;
  cacheExpiry = 0;
}

/**
 * Thêm tenant_id vào dữ liệu
 * @param {Object|Array} data - Dữ liệu cần thêm tenant_id
 * @param {string} tenantId - tenant_id cần thêm
 * @returns {Object|Array} - Dữ liệu đã thêm tenant_id
 */
export function addTenantIdToData(data, tenantId) {
  if (!tenantId) return data;

  // Nếu data là mảng, thêm tenant_id vào từng phần tử
  if (Array.isArray(data)) {
    return data.map((item) => ({
      ...item,
      tenantId,
    }));
  }

  // Nếu data là object, thêm tenant_id vào object
  return {
    ...data,
    tenantId,
  };
}

/**
 * Thêm tenant_id vào bộ lọc
 * @param {Object} filters - Bộ lọc cần thêm tenant_id
 * @param {string} tenantId - tenant_id cần thêm
 * @returns {Object} - Bộ lọc đã thêm tenant_id
 */
export function addTenantIdToFilters(filters, tenantId) {
  if (!tenantId) return filters;

  return {
    ...filters,
    tenantId,
  };
}

// Danh sách các bảng không cần thêm tenant_id
const EXCLUDED_TABLES = [
  'tenants',
  'users',
  'roles',
  'permissions',
  'user_roles',
  'user_permissions',
  'role_permissions',
  'credit_packages', // Thêm bảng credit_packages vào danh sách loại trừ
];

/**
 * Kiểm tra xem bảng có cần thêm tenant_id không
 * @param {string} table - Tên bảng
 * @returns {boolean} - true nếu bảng cần thêm tenant_id
 */
export function shouldAddTenantId(table) {
  return !EXCLUDED_TABLES.includes(table);
}
