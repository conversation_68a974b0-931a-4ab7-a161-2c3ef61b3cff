'use client';

import crypto from 'crypto';

import { getCachedTenantId } from './tenant-middleware';
import { fetchData, createData, updateData } from './supabase-utils';
import {
  PAYMENT_STATUSES,
  TENANT_CREDITS_TABLE,
  CREDIT_PACKAGES_TABLE,
  CREDIT_PAYMENTS_TABLE,
  CREDIT_TRANSACTION_TYPES,
  CREDIT_TRANSACTIONS_TABLE,
} from './credit-constants';

// Helper function để xử lý lỗi tham số đầu vào
const validateParam = (param, paramName) => {
  if (!param) {
    return { success: false, error: `${paramName} is required`, data: null };
  }
  return null;
};

/**
 * Lấy danh sách gói credit với các tùy chọn lọc
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getCreditPackages(options = {}) {
  const defaultOptions = {
    orderBy: 'sortOrder',
    ascending: true,
    filters: { isActive: true },
    ...options,
  };

  // Bảng credit_packages đã được thêm vào EXCLUDED_TABLES trong tenant-middleware.js
  // nên không cần thêm tenant_id vào filters
  return fetchData(CREDIT_PACKAGES_TABLE, defaultOptions);
}

/**
 * Lấy thông tin chi tiết gói credit theo ID
 * @param {string} packageId - ID gói credit
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getCreditPackageById(packageId) {
  const validationError = validateParam(packageId, 'packageId');
  if (validationError) return validationError;

  return fetchData(CREDIT_PACKAGES_TABLE, {
    filters: { id: packageId },
    single: true,
  });
}

/**
 * Lấy số dư credit của tenant
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getTenantCredit() {
  // Không cần truyền tenantId vì supabase-utils sẽ tự động thêm tenant_id vào filters
  return fetchData(TENANT_CREDITS_TABLE, {
    single: true,
  });
}

/**
 * Tạo hoặc cập nhật số dư credit của tenant
 * @param {number} balance - Số dư credit mới
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateTenantCredit(balance) {
  // Lấy tenant_id từ cache (supabase-utils sẽ tự động thêm tenant_id)
  const tenantId = await getCachedTenantId();
  if (!tenantId) {
    return { success: false, error: 'tenant_id is required', data: null };
  }

  // Kiểm tra xem tenant đã có bản ghi credit chưa
  const { data: existingCredit } = await getTenantCredit();

  if (existingCredit) {
    // Cập nhật số dư hiện tại
    return updateData(
      TENANT_CREDITS_TABLE,
      {
        balance,
        lastUpdated: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      { id: existingCredit.id }
    );
  }

  // Tạo mới bản ghi credit cho tenant
  return createData(TENANT_CREDITS_TABLE, {
    balance,
    lastUpdated: new Date().toISOString(),
  });
}

/**
 * Thêm credit transaction
 * @param {Object} transactionData - Dữ liệu giao dịch
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function addCreditTransaction(transactionData) {
  const { amount, type, description, referenceId, referenceType, createdBy } = transactionData;

  // Lấy tenant_id từ cache (supabase-utils sẽ tự động thêm tenant_id)
  const tenantId = await getCachedTenantId();
  if (!tenantId) {
    return { success: false, error: 'tenant_id is required', data: null };
  }

  // Lấy số dư hiện tại của tenant
  const { data: tenantCredit } = await getTenantCredit();

  // Nếu tenant chưa có bản ghi credit, tạo mới với số dư 0
  const currentBalance = tenantCredit ? tenantCredit.balance : 0;

  // Tính toán số dư mới
  const newBalance = parseFloat(currentBalance) + parseFloat(amount);

  // Tạo transaction
  // Không cần truyền tenantId vì supabase-utils sẽ tự động thêm tenant_id
  const transactionResult = await createData(CREDIT_TRANSACTIONS_TABLE, {
    amount,
    balanceBefore: currentBalance,
    balanceAfter: newBalance,
    type,
    description,
    referenceId,
    referenceType,
    createdBy,
  });

  // Cập nhật số dư của tenant
  if (transactionResult.success) {
    await updateTenantCredit(tenantId, newBalance);
  }

  return transactionResult;
}

/**
 * Lấy lịch sử giao dịch credit của tenant
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getCreditTransactions(options = {}) {
  // Không cần truyền tenantId vì supabase-utils sẽ tự động thêm tenant_id vào filters
  const defaultOptions = {
    orderBy: 'createdAt',
    ascending: false,
    ...options,
  };

  return fetchData(CREDIT_TRANSACTIONS_TABLE, defaultOptions);
}

/**
 * Tạo thanh toán credit mới
 * @param {Object} paymentData - Dữ liệu thanh toán
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createCreditPayment(paymentData) {
  const { packageId, amount, creditAmount, paymentMethod, createdBy } = paymentData;

  // Không cần kiểm tra tenantId vì supabase-utils sẽ tự động thêm tenant_id
  if (!packageId) {
    return { success: false, error: 'packageId is required', data: null };
  }


  // Tạo payment với trạng thái pending
  // Không cần truyền tenantId vì supabase-utils sẽ tự động thêm tenant_id
  return createData(CREDIT_PAYMENTS_TABLE, {
    packageId,
    amount,
    creditAmount,
    paymentMethod,
    paymentStatus: PAYMENT_STATUSES.PENDING,
    createdBy,
    // Không cần thêm order_code vì sẽ được tự động tạo bởi sequence trong database
  });
}

/**
 * Lấy thông tin thanh toán theo ID
 * @param {string} paymentId - ID của thanh toán
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getPaymentById(paymentId) {
  const validationError = validateParam(paymentId, 'paymentId');
  if (validationError) return validationError;

  return fetchData(CREDIT_PAYMENTS_TABLE, {
    filters: { id: paymentId },
    single: true,
  });
}

/**
 * Lấy thông tin thanh toán theo order_code
 * @param {number} orderCode - Mã đơn hàng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getPaymentByOrderCode(orderCode) {
  const validationError = validateParam(orderCode, 'orderCode');
  if (validationError) return validationError;

  return fetchData(CREDIT_PAYMENTS_TABLE, {
    filters: { orderCode },
    single: true,
  });
}

/**
 * Cập nhật dữ liệu thanh toán
 * @param {string} paymentId - ID của thanh toán
 * @param {Object} dataToUpdate - Dữ liệu cần cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updatePaymentData(paymentId, dataToUpdate) {
  const validationError = validateParam(paymentId, 'paymentId');
  if (validationError) return validationError;

  return updateData(
    CREDIT_PAYMENTS_TABLE,
    {
      ...dataToUpdate,
      updatedAt: new Date().toISOString(),
    },
    { id: paymentId }
  );
}

/**
 * Cập nhật trạng thái thanh toán
 * @param {string} paymentId - ID của thanh toán
 * @param {string} status - Trạng thái mới
 * @param {Object} paymentData - Dữ liệu thanh toán bổ sung
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updatePaymentStatus(paymentId, status, paymentData = {}) {
  const validationError = validateParam(paymentId, 'paymentId');
  if (validationError) return validationError;

  // Lấy thông tin thanh toán hiện tại
  const { data: payment } = await getPaymentById(paymentId);

  if (!payment) {
    return { success: false, error: 'Payment not found', data: null };
  }

  // Nếu thanh toán đã hoàn thành và status mới cũng là completed, không làm gì cả
  if (payment.paymentStatus === PAYMENT_STATUSES.COMPLETED && status === PAYMENT_STATUSES.COMPLETED) {
    return { success: true, data: payment, error: null };
  }

  // Cập nhật trạng thái thanh toán
  const updateResult = await updateData(
    CREDIT_PAYMENTS_TABLE,
    {
      paymentStatus: status,
      paymentId: paymentData.paymentId || payment.paymentId,
      paymentData: paymentData || payment.paymentData,
      updatedAt: new Date().toISOString(),
    },
    { id: paymentId }
  );

  // Nếu thanh toán thành công và chưa có transaction, thêm credit cho tenant
  if (status === PAYMENT_STATUSES.COMPLETED && !payment.transactionId) {
    // Thêm transaction
    const transactionResult = await addCreditTransaction({
      amount: payment.creditAmount,
      type: CREDIT_TRANSACTION_TYPES.PURCHASE,
      description: `Mua ${payment.creditAmount} credit`,
      referenceId: payment.id,
      referenceType: 'payment',
      createdBy: payment.createdBy,
    });

    if (transactionResult.success) {
      // Cập nhật transaction_id trong payment
      await updateData(
        CREDIT_PAYMENTS_TABLE,
        { transactionId: transactionResult.data.id },
        { id: paymentId }
      );
    }
  }

  return updateResult;
}

/**
 * Lấy lịch sử thanh toán credit của tenant
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getCreditPayments(options = {}) {
  // Không cần truyền tenantId vì supabase-utils sẽ tự động thêm tenant_id vào filters
  const defaultOptions = {
    orderBy: 'createdAt',
    ascending: false,
    ...options,
  };

  return fetchData(CREDIT_PAYMENTS_TABLE, defaultOptions);
}

/**
 * Sử dụng credit cho một hoạt động
 * @param {number} amount - Số credit sử dụng (số âm)
 * @param {string} description - Mô tả hoạt động
 * @param {string} referenceId - ID tham chiếu (nếu có)
 * @param {string} referenceType - Loại tham chiếu (nếu có)
 * @param {string} createdBy - ID người thực hiện
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function useCredit(amount, description, referenceId, referenceType, createdBy) {
  // Lấy tenant_id từ cache (supabase-utils sẽ tự động thêm tenant_id)
  const tenantId = await getCachedTenantId();
  if (!tenantId) {
    return { success: false, error: 'tenant_id is required', data: null };
  }

  // Đảm bảo amount là số âm
  const creditAmount = Math.abs(amount) * -1;

  // Lấy số dư hiện tại của tenant
  const { data: tenantCredit } = await getTenantCredit();

  if (!tenantCredit) {
    return {
      success: false,
      error: 'Tenant does not have any credit',
      data: null
    };
  }

  // Kiểm tra xem số dư có đủ không
  if (parseFloat(tenantCredit.balance) + parseFloat(creditAmount) < 0) {
    return {
      success: false,
      error: 'Insufficient credit balance',
      data: null
    };
  }

  // Thêm transaction với loại 'usage'
  return addCreditTransaction({
    amount: creditAmount,
    type: CREDIT_TRANSACTION_TYPES.USAGE,
    description,
    referenceId,
    referenceType,
    createdBy,
  });
}
