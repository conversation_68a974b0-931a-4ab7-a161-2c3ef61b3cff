# Mooly Chatbot Services

Các service trong thư mục này cung cấp các hàm và hook để tương tác với Supabase.

## C<PERSON>u trúc

```
src/actions/mooly-chatbot/
├── supabase-utils.js    # C<PERSON>c hàm tiện ích chung để tương tác với Supabase
├── store-service.js     # Service quản lý cửa hàng
├── product-service.js   # Service quản lý sản phẩm
├── category-service.js  # Service quản lý danh mục
├── order-service.js     # Service quản lý đơn hàng
├── chatbot-service.js   # Service quản lý cấu hình chatbot
└── index.js             # File xuất tất cả các service
```

## Cách sử dụng

### 1. Import các service

```javascript
import {
  // Store service
  getStores,
  getStoreById,
  getUserStores,
  createStore,
  updateStore,
  deleteStore,
  upsertStore,
  addUserToStore,
  removeUserFromStore,
  updateUserStoreRole,
  getStoreUsers,
  useStores,
  useStore,
  useUserStores,
  useStoreUsers,
  useStoreMutations,
  
  // Product service
  getProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  upsertProduct,
  useProducts,
  useProduct,
  useProductMutations,
  
  // Category service
  getCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory,
  upsertCategory,
  useCategories,
  useCategory,
  useCategoryMutations,
  
  // Order service
  getOrders,
  getOrderById,
  createOrder,
  updateOrder,
  deleteOrder,
  upsertOrder,
  useOrders,
  useOrder,
  useOrderMutations,
  
  // Chatbot service
  getChatbots,
  getChatbotById,
  getChatbotByStoreId,
  createChatbot,
  updateChatbot,
  deleteChatbot,
  upsertChatbot,
  useChatbots,
  useChatbot,
  useChatbotByStore,
  useChatbotMutations,
} from 'src/actions/mooly-chatbot';
```

### 2. Sử dụng Store Service

#### 2.1. Lấy danh sách cửa hàng

```javascript
// Sử dụng hàm trực tiếp
const fetchStores = async () => {
  const result = await getStores({
    filters: { is_active: true },
    orderBy: 'created_at',
    ascending: false,
    limit: 10,
  });
  
  if (result.success) {
    console.log('Danh sách cửa hàng:', result.data);
  } else {
    console.error('Lỗi khi lấy danh sách cửa hàng:', result.error);
  }
};

// Sử dụng hook SWR
function StoreList() {
  const { stores, isLoading, error } = useStores({
    filters: { is_active: true },
    orderBy: 'created_at',
    ascending: false,
    limit: 10,
  });
  
  if (isLoading) return <div>Đang tải...</div>;
  if (error) return <div>Lỗi: {error.message}</div>;
  
  return (
    <div>
      <h1>Danh sách cửa hàng</h1>
      <ul>
        {stores.map((store) => (
          <li key={store.id}>{store.name}</li>
        ))}
      </ul>
    </div>
  );
}
```

#### 2.2. Lấy chi tiết cửa hàng

```javascript
// Sử dụng hàm trực tiếp
const fetchStoreDetails = async (storeId) => {
  const result = await getStoreById(storeId);
  
  if (result.success) {
    console.log('Chi tiết cửa hàng:', result.data);
  } else {
    console.error('Lỗi khi lấy chi tiết cửa hàng:', result.error);
  }
};

// Sử dụng hook SWR
function StoreDetails({ storeId }) {
  const { store, isLoading, error } = useStore(storeId);
  
  if (isLoading) return <div>Đang tải...</div>;
  if (error) return <div>Lỗi: {error.message}</div>;
  if (!store) return <div>Không tìm thấy cửa hàng</div>;
  
  return (
    <div>
      <h1>{store.name}</h1>
      <p>Địa chỉ: {store.address}</p>
      <p>Điện thoại: {store.phone}</p>
      <p>Email: {store.email}</p>
    </div>
  );
}
```

#### 2.3. Lấy danh sách cửa hàng của người dùng

```javascript
// Sử dụng hàm trực tiếp
const fetchUserStores = async (userId) => {
  const result = await getUserStores(userId);
  
  if (result.success) {
    console.log('Danh sách cửa hàng của người dùng:', result.data);
  } else {
    console.error('Lỗi khi lấy danh sách cửa hàng của người dùng:', result.error);
  }
};

// Sử dụng hook SWR
function UserStoreList({ userId }) {
  const { stores, isLoading, error } = useUserStores(userId);
  
  if (isLoading) return <div>Đang tải...</div>;
  if (error) return <div>Lỗi: {error.message}</div>;
  
  return (
    <div>
      <h1>Danh sách cửa hàng của bạn</h1>
      <ul>
        {stores.map((store) => (
          <li key={store.id}>
            {store.name} - {store.role} {store.isManager ? '(Quản lý)' : ''}
          </li>
        ))}
      </ul>
    </div>
  );
}
```

#### 2.4. Tạo, cập nhật, xóa cửa hàng

```javascript
// Sử dụng hàm trực tiếp
const createNewStore = async (storeData) => {
  const result = await createStore(storeData);
  
  if (result.success) {
    console.log('Cửa hàng đã được tạo:', result.data);
  } else {
    console.error('Lỗi khi tạo cửa hàng:', result.error);
  }
};

const updateStoreInfo = async (storeId, storeData) => {
  const result = await updateStore(storeId, storeData);
  
  if (result.success) {
    console.log('Cửa hàng đã được cập nhật:', result.data);
  } else {
    console.error('Lỗi khi cập nhật cửa hàng:', result.error);
  }
};

const removeStore = async (storeId) => {
  const result = await deleteStore(storeId);
  
  if (result.success) {
    console.log('Cửa hàng đã được xóa');
  } else {
    console.error('Lỗi khi xóa cửa hàng:', result.error);
  }
};

// Sử dụng hook SWR Mutation
function StoreManagement() {
  const {
    createStore,
    updateStore,
    deleteStore,
    isCreating,
    isUpdating,
    isDeleting,
  } = useStoreMutations();
  
  const handleCreateStore = async (storeData) => {
    const result = await createStore(storeData);
    if (result.success) {
      console.log('Cửa hàng đã được tạo');
    }
  };
  
  const handleUpdateStore = async (storeId, storeData) => {
    const result = await updateStore(storeId, storeData);
    if (result.success) {
      console.log('Cửa hàng đã được cập nhật');
    }
  };
  
  const handleDeleteStore = async (storeId) => {
    const result = await deleteStore(storeId);
    if (result.success) {
      console.log('Cửa hàng đã được xóa');
    }
  };
  
  return (
    <div>
      <button onClick={() => handleCreateStore({ name: 'Cửa hàng mới' })} disabled={isCreating}>
        {isCreating ? 'Đang tạo...' : 'Tạo cửa hàng mới'}
      </button>
      
      <button onClick={() => handleUpdateStore('store-id', { name: 'Tên mới' })} disabled={isUpdating}>
        {isUpdating ? 'Đang cập nhật...' : 'Cập nhật cửa hàng'}
      </button>
      
      <button onClick={() => handleDeleteStore('store-id')} disabled={isDeleting}>
        {isDeleting ? 'Đang xóa...' : 'Xóa cửa hàng'}
      </button>
    </div>
  );
}
```

#### 2.5. Quản lý người dùng trong cửa hàng

```javascript
// Sử dụng hàm trực tiếp
const addUser = async (storeId, userId, role, isManager) => {
  const result = await addUserToStore(storeId, userId, role, isManager);
  
  if (result.success) {
    console.log('Người dùng đã được thêm vào cửa hàng:', result.data);
  } else {
    console.error('Lỗi khi thêm người dùng vào cửa hàng:', result.error);
  }
};

const removeUser = async (storeId, userId) => {
  const result = await removeUserFromStore(storeId, userId);
  
  if (result.success) {
    console.log('Người dùng đã được xóa khỏi cửa hàng');
  } else {
    console.error('Lỗi khi xóa người dùng khỏi cửa hàng:', result.error);
  }
};

const updateRole = async (storeId, userId, role, isManager) => {
  const result = await updateUserStoreRole(storeId, userId, role, isManager);
  
  if (result.success) {
    console.log('Vai trò của người dùng đã được cập nhật:', result.data);
  } else {
    console.error('Lỗi khi cập nhật vai trò của người dùng:', result.error);
  }
};

// Sử dụng hook SWR
function StoreUserList({ storeId }) {
  const { users, isLoading, error, mutate } = useStoreUsers(storeId);
  
  const {
    addUserToStore,
    removeUserFromStore,
    updateUserStoreRole,
    isAddingUser,
    isRemovingUser,
    isUpdatingRole,
  } = useStoreMutations();
  
  const handleAddUser = async (userId, role, isManager) => {
    const result = await addUserToStore(storeId, userId, role, isManager);
    if (result.success) {
      mutate(); // Làm mới danh sách người dùng
    }
  };
  
  const handleRemoveUser = async (userId) => {
    const result = await removeUserFromStore(storeId, userId);
    if (result.success) {
      mutate(); // Làm mới danh sách người dùng
    }
  };
  
  const handleUpdateRole = async (userId, role, isManager) => {
    const result = await updateUserStoreRole(storeId, userId, role, isManager);
    if (result.success) {
      mutate(); // Làm mới danh sách người dùng
    }
  };
  
  if (isLoading) return <div>Đang tải...</div>;
  if (error) return <div>Lỗi: {error.message}</div>;
  
  return (
    <div>
      <h1>Danh sách người dùng trong cửa hàng</h1>
      <ul>
        {users.map((user) => (
          <li key={user.id}>
            {user.email} - {user.role} {user.isManager ? '(Quản lý)' : ''}
            <button onClick={() => handleUpdateRole(user.id, 'admin', true)} disabled={isUpdatingRole}>
              Đặt làm quản lý
            </button>
            <button onClick={() => handleRemoveUser(user.id)} disabled={isRemovingUser}>
              Xóa khỏi cửa hàng
            </button>
          </li>
        ))}
      </ul>
      
      <button onClick={() => handleAddUser('user-id', 'viewer', false)} disabled={isAddingUser}>
        Thêm người dùng mới
      </button>
    </div>
  );
}
```

## Lưu ý

1. Tất cả các hàm đều tự động xử lý việc chuyển đổi giữa snake_case (database) và camelCase (UI).
2. Các hook SWR giúp tối ưu hiệu suất bằng cách cache dữ liệu và tự động làm mới khi cần thiết.
3. Các hàm mutation trả về kết quả với cấu trúc `{ success, data, error }` để dễ dàng xử lý.
4. Đảm bảo truyền đúng các tham số bắt buộc cho mỗi hàm để tránh lỗi.
