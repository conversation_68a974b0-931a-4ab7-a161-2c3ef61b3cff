'use client';

import { useRef, useMemo, useState, useEffect, useCallback } from 'react';

import { fetchData, createData, updateData, deleteData } from './supabase-utils';

// Tên bảng trong Supabase
const TABLE_NAME = 'mooly_accounts';

/**
 * L<PERSON>y thông tin tài khoản Mooly của tenant
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getMoolyAccount(options = {}) {
  try {
    // Trước tiên, kiểm tra xem có dữ liệu nào không
    const result = await fetchData(TABLE_NAME, {
      ...options,
      single: false, // Không sử dụng single để tránh lỗi khi không có dữ liệu
    });

    if (!result.success) {
      return result;
    }

    // Nếu có dữ liệu, tr<PERSON> về phần tử đầu tiên
    if (result.data && Array.isArray(result.data) && result.data.length > 0) {
      return {
        success: true,
        data: result.data[0],
        error: null,
      };
    }

    // Nếu không có dữ liệu, trả về null nhưng vẫn success
    return {
      success: true,
      data: null,
      error: null,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Failed to fetch Mooly account',
      data: null,
    };
  }
}

/**
 * Tạo tài khoản Mooly mới
 * @param {Object} accountData - Dữ liệu tài khoản
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createMoolyAccount(accountData) {
  return createData(TABLE_NAME, accountData);
}

/**
 * Cập nhật tài khoản Mooly
 * @param {string} accountId - ID của tài khoản
 * @param {Object} accountData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateMoolyAccount(accountId, accountData) {
  if (!accountId) return { success: false, error: 'Account ID is required', data: null };

  return updateData(TABLE_NAME, accountData, { id: accountId });
}

/**
 * Xóa tài khoản Mooly
 * @param {string} accountId - ID của tài khoản
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteMoolyAccount(accountId) {
  if (!accountId) return { success: false, error: 'Account ID is required', data: null };

  return deleteData(TABLE_NAME, { id: accountId });
}

/**
 * Hook để lấy thông tin tài khoản Mooly
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Object} - Kết quả và các hàm tương tác
 */
export function useMoolyAccount(options = {}) {
  const [account, setAccount] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Lấy dữ liệu tài khoản với các tùy chọn ổn định
  const accountOptions = useMemo(() => ({ ...options }), [options]);

  // Sử dụng ref để theo dõi nếu component đã unmount
  const isMounted = useRef(true);

  const fetchAccount = useCallback(async () => {
    // Tránh gọi API nhiều lần liên tiếp
    if (isLoading) return;

    // Sử dụng biến để theo dõi nếu request đã bị hủy
    let isCancelled = false;

    setIsLoading(true);
    try {
      const result = await getMoolyAccount(accountOptions);
      // Kiểm tra nếu component vẫn mounted và request chưa bị hủy
      if (isMounted.current && !isCancelled) {
        if (result.success) {
          setAccount(result.data);
        } else {
          setError(result.error);
        }
      }
    } catch (err) {
      if (isMounted.current && !isCancelled) {
        setError(err.message || 'Failed to fetch Mooly account');
      }
    } finally {
      if (isMounted.current && !isCancelled) {
        setIsLoading(false);
      }
    }

    return () => {
      isCancelled = true;
    };
  }, [accountOptions, isLoading]); // Phụ thuộc vào accountOptions và isLoading

  // Tải dữ liệu khi component được mount hoặc options thay đổi
  useEffect(() => {
    // Đánh dấu component đã mount
    isMounted.current = true;

    // Gọi fetchAccount để tải dữ liệu ban đầu
    if (!isLoading) {
      fetchAccount();
    }

    // Cleanup function để đánh dấu component đã unmount
    return () => {
      isMounted.current = false;
    };
  }, [fetchAccount, isLoading]); // Phụ thuộc vào fetchAccount và isLoading

  // Hàm để làm mới dữ liệu
  const mutate = useCallback(() => {
    // Tránh gọi API nếu đang loading
    if (!isLoading) {
      fetchAccount();
    }
  }, [fetchAccount, isLoading]); // Phụ thuộc vào fetchAccount và isLoading

  return {
    account,
    isLoading,
    error,
    mutate,
  };
}

/**
 * Hook để quản lý các thao tác với tài khoản Mooly
 * @returns {Object} - Các hàm tương tác và trạng thái
 */
export function useMoolyAccountMutations() {
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState(null);

  // Hàm tạo tài khoản Mooly
  const createAccount = useCallback(async (accountData) => {
    setIsCreating(true);
    setError(null);
    try {
      const result = await createMoolyAccount(accountData);
      if (!result.success) {
        setError(result.error);
      }
      return result;
    } catch (err) {
      setError(err.message || 'Failed to create Mooly account');
      return { success: false, error: err.message, data: null };
    } finally {
      setIsCreating(false);
    }
  }, []);

  // Hàm cập nhật tài khoản Mooly
  const updateAccount = useCallback(async (accountId, accountData) => {
    setIsUpdating(true);
    setError(null);
    try {
      const result = await updateMoolyAccount(accountId, accountData);
      if (!result.success) {
        setError(result.error);
      }
      return result;
    } catch (err) {
      setError(err.message || 'Failed to update Mooly account');
      return { success: false, error: err.message, data: null };
    } finally {
      setIsUpdating(false);
    }
  }, []);

  // Hàm xóa tài khoản Mooly
  const deleteAccount = useCallback(async (accountId) => {
    setIsDeleting(true);
    setError(null);
    try {
      const result = await deleteMoolyAccount(accountId);
      if (!result.success) {
        setError(result.error);
      }
      return result;
    } catch (err) {
      setError(err.message || 'Failed to delete Mooly account');
      return { success: false, error: err.message, data: null };
    } finally {
      setIsDeleting(false);
    }
  }, []);

  return {
    createAccount,
    updateAccount,
    deleteAccount,
    isCreating,
    isUpdating,
    isDeleting,
    error,
  };
}
