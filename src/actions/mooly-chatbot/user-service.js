'use client';

import { useState } from 'react';

import { supabase } from 'src/lib/supabase';

import {
  callRPC,
  fetchData,
  createData,
  updateData,
  deleteData,
  upsertData,
} from './supabase-utils';

// Tên bảng trong Supabase
const TABLE_NAME = 'users';

// Helper function để xử lý lỗi tham số đầu vào
const validateParam = (param, paramName) => {
  if (!param) {
    return { success: false, error: `${paramName} is required`, data: null };
  }
  return null;
};

/**
 * Lấy danh sách người dùng với các tùy chọn lọc
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getUsers(options = {}) {
  return fetchData(TABLE_NAME, options);
}

/**
 * Lấy chi tiết một người dùng theo ID
 * @param {string} userId - ID của người dùng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getUserById(userId) {
  const validationError = validateParam(userId, 'User ID');
  if (validationError) return validationError;

  return fetchData(TABLE_NAME, {
    filters: { id: userId },
    single: true,
  });
}

/**
 * Lấy thông tin chi tiết của người dùng bao gồm vai trò, quyền và cửa hàng
 * @param {string} userId - ID của người dùng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getUserDetails(userId) {
  const validationError = validateParam(userId, 'User ID');
  if (validationError) return validationError;

  return callRPC('get_user_complete_details', { user_id_param: userId });
}

/**
 * Tạo người dùng mới trong bảng users
 * @param {Object} userData - Dữ liệu người dùng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createUser(userData) {
  return createData(TABLE_NAME, userData);
}

/**
 * Tạo người dùng mới trong Supabase Auth và bảng users
 * @param {Object} userData - Dữ liệu người dùng (email, password, fullName, phone)
 * @param {boolean} emailConfirm - Xác nhận email ngay lập tức
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createAuthUser(userData) {
  try {
    const { email, password, fullName, phone, emailConfirm = false } = userData;

    // Tạo người dùng trong Supabase Auth
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email,
      password: password || 'Tho123456@', // Mật khẩu mặc định nếu không cung cấp
      email_confirm: emailConfirm,
      phone,
      phone_confirm: !!phone,
      user_metadata: {
        full_name: fullName,
        phone,
      },
    });

    if (authError) {
      console.error('Error creating auth user:', authError);
      return { success: false, error: authError, data: null };
    }

    // Tạo hoặc cập nhật thông tin trong bảng users
    const userRecord = {
      id: authUser.user.id,
      email: authUser.user.email,
      full_name: fullName,
      phone: phone || null,
      is_active: true,
    };

    const { data: dbUserData, error: userError } = await upsertData(TABLE_NAME, userRecord);

    if (userError) {
      console.error('Error creating user record:', userError);
      return { success: false, error: userError, data: null };
    }

    return {
      success: true,
      data: {
        authUser: authUser.user,
        userRecord: dbUserData,
      },
      error: null,
    };
  } catch (error) {
    console.error('Error in createAuthUser:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Cập nhật người dùng
 * @param {string} userId - ID của người dùng
 * @param {Object} userData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateUser(userId, userData) {
  const validationError = validateParam(userId, 'User ID');
  if (validationError) return validationError;

  return updateData(TABLE_NAME, userData, { id: userId });
}

/**
 * Xóa người dùng
 * @param {string} userId - ID của người dùng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteUser(userId) {
  const validationError = validateParam(userId, 'User ID');
  if (validationError) return validationError;

  return deleteData(TABLE_NAME, { id: userId });
}

/**
 * Tạo hoặc cập nhật người dùng
 * @param {Object} userData - Dữ liệu người dùng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertUser(userData) {
  return upsertData(TABLE_NAME, userData);
}

/**
 * Gán vai trò cho người dùng
 * @param {string} userId - ID của người dùng
 * @param {string} roleId - ID của vai trò
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function assignRoleToUser(userId, roleId) {
  const userIdError = validateParam(userId, 'User ID');
  if (userIdError) return userIdError;

  const roleIdError = validateParam(roleId, 'Role ID');
  if (roleIdError) return roleIdError;

  return callRPC('assign_role_to_user', {
    user_id_param: userId,
    role_id_param: roleId,
  });
}

/**
 * Xóa vai trò của người dùng
 * @param {string} userId - ID của người dùng
 * @param {string} roleId - ID của vai trò
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function removeRoleFromUser(userId, roleId) {
  const userIdError = validateParam(userId, 'User ID');
  if (userIdError) return userIdError;

  const roleIdError = validateParam(roleId, 'Role ID');
  if (roleIdError) return roleIdError;

  return callRPC('remove_role_from_user', {
    user_id_param: userId,
    role_id_param: roleId,
  });
}

/**
 * Gán quyền cho người dùng
 * @param {string} userId - ID của người dùng
 * @param {string} permissionId - ID của quyền
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function assignPermissionToUser(userId, permissionId) {
  const userIdError = validateParam(userId, 'User ID');
  if (userIdError) return userIdError;

  const permissionIdError = validateParam(permissionId, 'Permission ID');
  if (permissionIdError) return permissionIdError;

  return callRPC('assign_permission_to_user', {
    user_id_param: userId,
    permission_id_param: permissionId,
  });
}

/**
 * Xóa quyền của người dùng
 * @param {string} userId - ID của người dùng
 * @param {string} permissionId - ID của quyền
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function removePermissionFromUser(userId, permissionId) {
  const userIdError = validateParam(userId, 'User ID');
  if (userIdError) return userIdError;

  const permissionIdError = validateParam(permissionId, 'Permission ID');
  if (permissionIdError) return permissionIdError;

  return callRPC('remove_permission_from_user', {
    user_id_param: userId,
    permission_id_param: permissionId,
  });
}

/**
 * Kiểm tra xem người dùng có quyền cụ thể không
 * @param {string} userId - ID của người dùng
 * @param {string} permissionCode - Mã quyền cần kiểm tra
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function checkUserPermission(userId, permissionCode) {
  const userIdError = validateParam(userId, 'User ID');
  if (userIdError) return userIdError;

  const permissionCodeError = validateParam(permissionCode, 'Permission code');
  if (permissionCodeError) return permissionCodeError;

  return callRPC('check_user_permission', {
    user_id_param: userId,
    permission_code_param: permissionCode,
  });
}

/**
 * Hook để tạo, cập nhật, xóa người dùng
 * @returns {Object} - Các hàm mutation
 */
export function useUserMutations() {
  const [loadingStates, setLoadingStates] = useState({
    creating: false,
    updating: false,
    deleting: false,
    upserting: false,
    assigningRole: false,
    removingRole: false,
    assigningPermission: false,
    removingPermission: false,
  });

  // Helper function to handle loading state and error handling
  const withLoadingState = async (stateKey, asyncFn) => {
    setLoadingStates((prev) => ({ ...prev, [stateKey]: true }));
    try {
      return await asyncFn();
    } finally {
      setLoadingStates((prev) => ({ ...prev, [stateKey]: false }));
    }
  };

  // Mutation functions using the helper
  const createUserMutation = (userData) => withLoadingState('creating', () => createUser(userData));

  const createAuthUserMutation = (userData) =>
    withLoadingState('creating', () => createAuthUser(userData));

  const updateUserMutation = (id, data) => withLoadingState('updating', () => updateUser(id, data));

  const deleteUserMutation = (userId) => withLoadingState('deleting', () => deleteUser(userId));

  const upsertUserMutation = (userData) =>
    withLoadingState('upserting', () => upsertUser(userData));

  const assignRoleToUserMutation = (userId, roleId) =>
    withLoadingState('assigningRole', () => assignRoleToUser(userId, roleId));

  const removeRoleFromUserMutation = (userId, roleId) =>
    withLoadingState('removingRole', () => removeRoleFromUser(userId, roleId));

  const assignPermissionToUserMutation = (userId, permissionId) =>
    withLoadingState('assigningPermission', () => assignPermissionToUser(userId, permissionId));

  const removePermissionFromUserMutation = (userId, permissionId) =>
    withLoadingState('removingPermission', () => removePermissionFromUser(userId, permissionId));

  // Calculate if any mutation is in progress
  const isMutating = Object.values(loadingStates).some(Boolean);

  return {
    createUser: createUserMutation,
    createAuthUser: createAuthUserMutation,
    updateUser: updateUserMutation,
    deleteUser: deleteUserMutation,
    upsertUser: upsertUserMutation,
    assignRoleToUser: assignRoleToUserMutation,
    removeRoleFromUser: removeRoleFromUserMutation,
    assignPermissionToUser: assignPermissionToUserMutation,
    removePermissionFromUser: removePermissionFromUserMutation,
    isCreating: loadingStates.creating,
    isUpdating: loadingStates.updating,
    isDeleting: loadingStates.deleting,
    isUpserting: loadingStates.upserting,
    isAssigningRole: loadingStates.assigningRole,
    isRemovingRole: loadingStates.removingRole,
    isAssigningPermission: loadingStates.assigningPermission,
    isRemovingPermission: loadingStates.removingPermission,
    isMutating,
  };
}
