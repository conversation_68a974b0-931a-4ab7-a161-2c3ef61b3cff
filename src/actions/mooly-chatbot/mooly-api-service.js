'use client';

import { useMemo, useState, useEffect, useCallback } from 'react';

/**
 * L<PERSON>y danh sách kênh từ API Mooly thông qua proxy
 * @param {string} accountId - ID tài khoản <PERSON>oly
 * @param {string} token - Token xác thực
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function fetchMoolyChannels(accountId, token) {
  if (!accountId || !token) {
    return { success: false, error: 'Account ID and token are required', data: [] };
  }

  try {
    // Sử dụng API proxy thay vì gọi trực tiếp đến Mooly API
    const response = await fetch(
      `/api/mooly-proxy/channels?accountId=${accountId}&token=${token}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    console.log(response);
    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      // eslint-disable-next-line no-unused-vars
      } catch (e) {
        errorData = { error: response.statusText };
      }

      return {
        success: false,
        error: errorData.error || 'Failed to fetch channels',
        data: [],
      };
    }

    const result = await response.json();
    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Failed to fetch channels',
      data: [],
    };
  }
}

/**
 * Hook để lấy danh sách kênh từ API Mooly
 * @param {string} accountId - ID tài khoản Mooly
 * @param {string} token - Token xác thực
 * @returns {Object} - Kết quả và các hàm tương tác
 */
export function useMoolyChannels(accountId, token) {
  const [channels, setChannels] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Sử dụng useMemo để tránh re-render không cần thiết
  const credentials = useMemo(() => ({ accountId, token }), [accountId, token]);

  const fetchChannels = useCallback(async () => {
    if (!credentials.accountId || !credentials.token) {
      setChannels([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const result = await fetchMoolyChannels(credentials.accountId, credentials.token);
      if (result.success) {
        setChannels(result.data || []);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message || 'Failed to fetch Mooly channels');
    } finally {
      setIsLoading(false);
    }
  }, [credentials]);

  // Tải dữ liệu khi component được mount hoặc credentials thay đổi
  useEffect(() => {
    // Chỉ gọi API khi có đủ thông tin và khi credentials thay đổi
    if (credentials.accountId && credentials.token) {
      // Sử dụng một biến để theo dõi nếu đã gọi API
      let isCancelled = false;

      // Sử dụng fetchChannels thay vì định nghĩa hàm mới để tránh trùng lặp logic
      const loadData = async () => {
        // Tránh gọi API nếu đã hủy hoặc đang loading
        if (isCancelled || isLoading) return;

        await fetchChannels();
      };

      loadData();

      // Cleanup function
      return () => {
        isCancelled = true;
      };
    } else {
      setChannels([]);
      setIsLoading(false);
    }
  }, [credentials, fetchChannels, isLoading]); // Thêm fetchChannels và isLoading vào dependencies

  // Hàm để làm mới dữ liệu
  const mutate = useCallback(() => {
    // Chỉ gọi API khi có đủ thông tin và không đang loading
    if (credentials.accountId && credentials.token && !isLoading) {
      fetchChannels();
    }
  }, [fetchChannels, credentials, isLoading]);

  return {
    channels,
    isLoading,
    error,
    mutate,
  };
}
