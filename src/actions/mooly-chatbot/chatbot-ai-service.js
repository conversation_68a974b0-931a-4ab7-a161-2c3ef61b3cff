'use client';

/**
 * Service để gọi API train chatbot
 */

/**
 * Gọi API để tạo dữ liệu training tối ưu từ dữ liệu người dùng
 * @param {Object} trainingData - Dữ liệu training từ form
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function generateOptimizedTrainingData(trainingData) {
  try {
    // Chuẩn bị dữ liệu gửi đi
    const requestData = {
      name: trainingData.name || '',
      infoItems: trainingData.infoItems || [],
      instructionItems: trainingData.instructionItems || [],
      instruction: trainingData.instruction || '',
    };

    // Gọi API
    const response = await fetch('/api/train-chatbot', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    // Xử lý kết quả
    const result = await response.json();

    if (!response.ok) {
      // <PERSON><PERSON><PERSON> về cả dữ liệu balance nếu lỗi là không đủ credit
      if (result.error && result.error.includes('Số dư credit không đủ')) {
        return {
          success: false,
          error: result.error,
          data: result.data || null, // Bao gồm thông tin về số dư hiện tại
          insufficientCredit: true, // Đánh dấu là lỗi không đủ credit
        };
      }

      return {
        success: false,
        error: result.error || 'Có lỗi xảy ra khi gọi API',
        data: null,
      };
    }

    return {
      success: true,
      data: result.data,
      message: result.message || 'Đã tạo dữ liệu training thành công!',
    };
  } catch (error) {
    console.error('Error generating optimized training data:', error);
    return {
      success: false,
      error: error.message || 'Có lỗi xảy ra khi xử lý dữ liệu',
      data: null,
    };
  }
}

/**
 * Lưu dữ liệu training đã được tối ưu hóa
 * @param {Object} optimizedData - Dữ liệu đã được tối ưu hóa
 * @param {Object} originalData - Dữ liệu gốc từ form
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function saveOptimizedTrainingData(optimizedData, originalData) {
  try {
    // Import service để lưu dữ liệu
    const { saveChatbotTrainingData } = await import('./chatbot-training-service');

    // Chuyển đổi FAQs thành infoItems
    const infoItems = optimizedData.faqs.map(faq => ({
      title: faq.topic,
      content: faq.content,
    }));

    // Chuẩn bị dữ liệu để lưu
    const trainingData = {
      ...originalData,
      instruction: optimizedData.instruction,
      infoItems,
      status: 'trained', // Đánh dấu là đã được training
    };

    // Lưu dữ liệu vào database
    return await saveChatbotTrainingData(trainingData);
  } catch (error) {
    console.error('Error saving optimized training data:', error);
    return {
      success: false,
      error: error.message || 'Có lỗi xảy ra khi lưu dữ liệu',
      data: null,
    };
  }
}
