'use client';

/**
 * Xử lý vấn đề hydration mismatch do browser extensions
 *
 * File này chứa các hàm tiện ích để xử lý vấn đề hydration mismatch
 * khi có sự khác biệt giữa server-side rendering và client-side rendering
 * do các browser extensions thêm vào.
 */

import { useBrowserExtensionHandler } from './browser-extensions';

/**
 * Component để xử lý các thuộc tính do browser extensions thêm vào
 * và các vấn đề hydration mismatch khác
 */
export function HydrationHandler({ children }) {
  // Sử dụng handler từ browser-extensions.js
  useBrowserExtensionHandler();

  return children;
}
