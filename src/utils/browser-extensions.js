'use client';

/**
 * X<PERSON> lý các vấn đề liên quan đến browser extensions
 * 
 * File này chứa các hàm tiện ích để xử lý các vấn đề có thể phát sinh
 * do các browser extensions gây ra, bao gồm:
 * 1. Hydration mismatch
 * 2. Thay đổi DOM không mong muốn
 * 3. Xung đột với các thư viện khác
 */

import { useEffect } from 'react';

/**
 * Danh sách các thuộc tính thường được thêm vào bởi các browser extensions
 */
const EXTENSION_ATTRIBUTES = [
  'data-atm-ext-installed',
  'data-atm-ext-version',
  'data-extension-id',
  'data-extension-version',
  // Thêm các thuộc tính khác nếu cần
];

/**
 * Danh sách các class thường được thêm vào bởi các browser extensions
 */
const EXTENSION_CLASSES = [
  'ext-installed',
  'ext-active',
  // Thêm các class khác nếu cần
];

/**
 * Hook để xử lý các thuộc tính và class do browser extensions thêm vào
 */
export function useBrowserExtensionHandler() {
  useEffect(() => {
    // Chỉ chạy ở phía client
    if (typeof window !== 'undefined') {
      // Xóa các thuộc tính từ thẻ body
      EXTENSION_ATTRIBUTES.forEach((attr) => {
        if (document.body.hasAttribute(attr)) {
          document.body.removeAttribute(attr);
        }
      });

      // Xóa các class từ thẻ body
      EXTENSION_CLASSES.forEach((className) => {
        if (document.body.classList.contains(className)) {
          document.body.classList.remove(className);
        }
      });

      // Xử lý các thay đổi DOM trong tương lai
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && 
              EXTENSION_ATTRIBUTES.includes(mutation.attributeName)) {
            document.body.removeAttribute(mutation.attributeName);
          }
        });
      });

      // Bắt đầu theo dõi các thay đổi thuộc tính trên thẻ body
      observer.observe(document.body, { attributes: true });

      // Dọn dẹp khi component unmount
      return () => {
        observer.disconnect();
      };
    }
  }, []);
}

/**
 * Component để xử lý các vấn đề do browser extensions gây ra
 */
export function BrowserExtensionHandler({ children }) {
  useBrowserExtensionHandler();
  return children;
}
