/**
 * Tiện ích xử lý ngày tháng cho dự án
 * 
 * File này cung cấp các hàm tiện ích và cấu hình chuẩn cho việc xử lý ngày tháng trong dự án.
 * Sử dụng dayjs làm thư viện chính để xử lý ngày tháng.
 * 
 * Quy ước:
 * - S<PERSON> dụng các hàm từ src/utils/format-time.js cho việc định dạng ngày tháng
 * - Sử dụng các mẫu định dạng từ formatPatterns trong src/utils/format-time.js
 * - Sử dụng locale từ cấu hình ngôn ngữ hiện tại của ứng dụng
 */

import dayjs from 'dayjs';

import { formatPatterns } from './format-time';

// ----------------------------------------------------------------------

/**
 * Các mẫu định dạng ngày tháng chuẩn cho dự án
 * Đ<PERSON><PERSON>c lấy từ formatPatterns trong src/utils/format-time.js
 */
export const DATE_FORMATS = {
  // Định dạng đầy đủ: 17 Apr 2022 12:00 am
  dateTime: formatPatterns.dateTime,
  
  // Định dạng ngày: 17 Apr 2022
  date: formatPatterns.date,
  
  // Định dạng giờ: 12:00 am
  time: formatPatterns.time,
  
  // Định dạng ngày tháng phân tách bằng dấu /: 17/04/2022
  splitDate: formatPatterns.split.date,
  
  // Định dạng ngày giờ phân tách bằng dấu /: 17/04/2022 12:00 am
  splitDateTime: formatPatterns.split.dateTime,
  
  // Định dạng ngày tháng phân tách bằng dấu -: 17-04-2022
  paramCaseDate: formatPatterns.paramCase.date,
  
  // Định dạng ngày giờ phân tách bằng dấu -: 17-04-2022 12:00 am
  paramCaseDateTime: formatPatterns.paramCase.dateTime,
  
  // Định dạng cho Việt Nam: DD/MM/YYYY
  viDate: 'DD/MM/YYYY',
  
  // Định dạng ngày giờ cho Việt Nam: DD/MM/YYYY HH:mm
  viDateTime: 'DD/MM/YYYY HH:mm',
};

/**
 * Kiểm tra xem một giá trị có phải là ngày hợp lệ hay không
 * @param {any} date - Giá trị cần kiểm tra
 * @returns {boolean} - true nếu là ngày hợp lệ, false nếu không phải
 */
export function isValidDate(date) {
  return date !== null && date !== undefined && dayjs(date).isValid();
}

/**
 * Chuyển đổi một giá trị thành đối tượng Date
 * @param {any} date - Giá trị cần chuyển đổi
 * @returns {Date|null} - Đối tượng Date hoặc null nếu không thể chuyển đổi
 */
export function toDate(date) {
  if (!isValidDate(date)) {
    return null;
  }
  return dayjs(date).toDate();
}

/**
 * Chuyển đổi một giá trị thành chuỗi ISO
 * @param {any} date - Giá trị cần chuyển đổi
 * @returns {string|null} - Chuỗi ISO hoặc null nếu không thể chuyển đổi
 */
export function toISOString(date) {
  if (!isValidDate(date)) {
    return null;
  }
  return dayjs(date).toISOString();
}

/**
 * Lấy ngày hiện tại
 * @returns {Date} - Ngày hiện tại
 */
export function getCurrentDate() {
  return dayjs().toDate();
}

/**
 * Lấy ngày đầu tiên của tháng
 * @param {any} date - Ngày tham chiếu (mặc định là ngày hiện tại)
 * @returns {Date} - Ngày đầu tiên của tháng
 */
export function getFirstDayOfMonth(date = null) {
  return dayjs(date || getCurrentDate()).startOf('month').toDate();
}

/**
 * Lấy ngày cuối cùng của tháng
 * @param {any} date - Ngày tham chiếu (mặc định là ngày hiện tại)
 * @returns {Date} - Ngày cuối cùng của tháng
 */
export function getLastDayOfMonth(date = null) {
  return dayjs(date || getCurrentDate()).endOf('month').toDate();
}

/**
 * Lấy ngày đầu tiên của tuần
 * @param {any} date - Ngày tham chiếu (mặc định là ngày hiện tại)
 * @returns {Date} - Ngày đầu tiên của tuần
 */
export function getFirstDayOfWeek(date = null) {
  return dayjs(date || getCurrentDate()).startOf('week').toDate();
}

/**
 * Lấy ngày cuối cùng của tuần
 * @param {any} date - Ngày tham chiếu (mặc định là ngày hiện tại)
 * @returns {Date} - Ngày cuối cùng của tuần
 */
export function getLastDayOfWeek(date = null) {
  return dayjs(date || getCurrentDate()).endOf('week').toDate();
}

/**
 * Thêm một khoảng thời gian vào ngày
 * @param {any} date - Ngày cần thêm
 * @param {number} amount - Số lượng
 * @param {string} unit - Đơn vị (day, month, year, hour, minute, second)
 * @returns {Date} - Ngày sau khi thêm
 */
export function addTime(date, amount, unit) {
  return dayjs(date).add(amount, unit).toDate();
}

/**
 * Trừ một khoảng thời gian từ ngày
 * @param {any} date - Ngày cần trừ
 * @param {number} amount - Số lượng
 * @param {string} unit - Đơn vị (day, month, year, hour, minute, second)
 * @returns {Date} - Ngày sau khi trừ
 */
export function subtractTime(date, amount, unit) {
  return dayjs(date).subtract(amount, unit).toDate();
}

/**
 * Tính khoảng cách giữa hai ngày
 * @param {any} date1 - Ngày thứ nhất
 * @param {any} date2 - Ngày thứ hai
 * @param {string} unit - Đơn vị (day, month, year, hour, minute, second)
 * @returns {number} - Khoảng cách giữa hai ngày
 */
export function getDiff(date1, date2, unit = 'day') {
  return dayjs(date1).diff(dayjs(date2), unit);
}
