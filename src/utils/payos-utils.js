import crypto from 'crypto';

/**
 * Sắp xếp đối tượng theo khóa
 * @param {Object} obj - <PERSON><PERSON><PERSON> tượng cần sắp xếp
 * @returns {Object} - <PERSON><PERSON><PERSON> tượng đã sắp xếp
 */
export function sortObjectByKey(obj) {
  return Object.keys(obj)
    .sort()
    .reduce((result, key) => {
      result[key] = obj[key];
      return result;
    }, {});
}

/**
 * Chuyển đổi đối tượng thành chuỗi truy vấn
 * @param {Object} obj - Đ<PERSON>i tượng cần chuyển đổi
 * @returns {string} - Chuỗi truy vấn
 */
export function convertObjectToQueryString(obj) {
  return Object.keys(obj)
    .filter((key) => obj[key] !== undefined)
    .map((key) => {
      let value = obj[key];

      // Sắp xếp mảng đối tượng lồng nhau
      if (value && Array.isArray(value)) {
        value = JSON.stringify(value.map((val) => sortObjectByKey(val)));
      }

      // Đặt chuỗi rỗng nếu giá trị là null hoặc undefined
      if ([null, undefined, 'undefined', 'null'].includes(value)) {
        value = '';
      }

      return `${key}=${value}`;
    })
    .join('&');
}

/**
 * Tạo chữ ký cho PayOS theo định dạng mới nhất
 * @param {Object} data - Dữ liệu cần tạo chữ ký
 * @param {string} checksumKey - Khóa checksum
 * @returns {string} - Chữ ký đã tạo
 */
export function createSignature(data, checksumKey) {
  try {
    // Lọc các trường cần thiết cho signature theo tài liệu PayOS
    const signatureData = {
      amount: data.amount,
      cancelUrl: data.cancelUrl,
      description: data.description,
      orderCode: data.orderCode,
      returnUrl: data.returnUrl,
    };

    // Sắp xếp dữ liệu theo alphabet
    const sortedData = sortObjectByKey(signatureData);

    // Chuyển đổi dữ liệu thành chuỗi truy vấn
    const queryString = convertObjectToQueryString(sortedData);

    // Tạo chữ ký từ dữ liệu
    const hmac = crypto.createHmac('sha256', checksumKey);
    return hmac.update(queryString).digest('hex');
  } catch (error) {
    console.error('Error creating signature:', error);
    throw new Error('Failed to create signature');
  }
}

/**
 * Xác minh chữ ký từ PayOS
 * @param {Object} data - Dữ liệu cần xác minh
 * @param {string} signature - Chữ ký cần kiểm tra
 * @param {string} checksumKey - Khóa checksum
 * @returns {boolean} - Kết quả xác minh
 */
export function verifySignature(data, signature, checksumKey) {
  try {
    // Sắp xếp dữ liệu theo khóa
    const sortedData = sortObjectByKey(data);

    // Chuyển đổi dữ liệu thành chuỗi truy vấn
    const queryString = convertObjectToQueryString(sortedData);

    // Tạo chữ ký từ dữ liệu
    const hmac = crypto.createHmac('sha256', checksumKey);
    const calculatedSignature = hmac.update(queryString).digest('hex');

    // So sánh chữ ký tính toán với chữ ký nhận được
    return calculatedSignature === signature;
  } catch (error) {
    console.error('Error verifying signature:', error);
    return false;
  }
}

/**
 * Xác minh dữ liệu webhook từ PayOS
 * @param {Object} webhookData - Dữ liệu webhook từ PayOS
 * @returns {Object} - Kết quả xác minh
 */
export function verifyWebhookData(webhookData) {
  try {
    // Kiểm tra dữ liệu webhook
    if (!webhookData || !webhookData.data) {
      return {
        success: false,
        error: 'Invalid webhook data',
        data: null,
      };
    }

    // Lấy checksumKey từ biến môi trường server-side
    const checksumKey = process.env.PAYOS_CHECKSUM_KEY;

    if (!checksumKey) {
      return {
        success: false,
        error: 'Checksum key is missing',
        data: null,
      };
    }

    // Xác minh chữ ký
    const isValidSignature = verifySignature(
      webhookData.data,
      webhookData.signature,
      checksumKey
    );

    if (!isValidSignature) {
      return {
        success: false,
        error: 'Invalid signature',
        data: null,
      };
    }

    // Trả về dữ liệu đã xác minh
    return {
      success: true,
      data: webhookData.data,
      error: null,
    };
  } catch (error) {
    console.error('PayOS webhook verification error:', error);
    return {
      success: false,
      error: error.message || 'Failed to verify webhook data',
      data: null,
    };
  }
}
